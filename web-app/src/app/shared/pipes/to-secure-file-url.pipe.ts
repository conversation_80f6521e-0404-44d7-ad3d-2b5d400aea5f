import { Pipe, PipeTransform } from '@angular/core';

import { Observable } from 'rxjs';

import { FileDownloadService } from '@/app/shared/services/file-download.service';

@Pipe({
  standalone: true,
  name: 'toSecureFileUrl',
  pure: false,
})
export class ToSecureFileUrlPipe implements PipeTransform {
  constructor(private readonly fileDownloadService: FileDownloadService) {}

  public transform(src: string): Observable<string | undefined | null> {
    return this.fileDownloadService.getObjectUrl$(src);
  }
}
