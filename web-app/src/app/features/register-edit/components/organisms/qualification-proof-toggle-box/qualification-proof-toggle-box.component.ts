import { AfterViewInit, ChangeDetectionStrategy, Component, EventEmitter, Output, ViewChild, inject, signal } from '@angular/core';
import { FormBuilder } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';

import { CertificateFormComponent } from '@/app/features/register-edit/components/organisms/certificate-form/certificate-form.component';
import { CertificateFormValues } from '@/app/features/register-edit/components/organisms/certificate-form/certificate-form.model';
import { LicenseFormComponent } from '@/app/features/register-edit/components/organisms/license-form/license-form.component';
import { LicenseFormValues } from '@/app/features/register-edit/components/organisms/license-form/license-form.models';
import { OtherQualificationsProofFormComponent } from '@/app/features/register-edit/components/organisms/other-qualifications-proof-form/other-qualifications-proof-form.component';
import { OtherQualificationsProofFormValues } from '@/app/features/register-edit/components/organisms/other-qualifications-proof-form/other-qualifications-proof-form.model';
import { IQualificationProofToggleBoxValues } from '@/app/features/register-edit/components/organisms/qualification-proof-toggle-box/qualification-proof-toggle-box.model';
import { QualificationProofFormGroup } from '@/app/features/register-edit/components/organisms/steps/qualification-proof-step/qualification-proof-step.models';
import { FormComponent } from '@/app/shared/atoms/form/form.component';
import { SliderButtonComponent } from '@/app/shared/atoms/slider-button/slider-button.component';
import { ToggleBoxContentComponent } from '@/app/shared/atoms/toggle-box-content/toggle-box-content.component';
import { ToggleBoxTabComponent } from '@/app/shared/atoms/toggle-box-tab/toggle-box-tab.component';
import { IconCertificateComponent } from '@/app/shared/icons/certificate/certificate.component';
import { IconLicenseCardComponent } from '@/app/shared/icons/license-card/license-card.component';
import { IconQualificationsProofComponent } from '@/app/shared/icons/qualifications-proof/qualifications-proof.component';
import { SliderButtonGroupComponent } from '@/app/shared/molecules/slider-button-group/slider-button-group.component';
import { ToggleBoxHeaderComponent } from '@/app/shared/molecules/toggle-box-header/toggle-box-header.component';
import { ToggleBoxComponent } from '@/app/shared/molecules/toggle-box/toggle-box.component';
import { FormToggleService } from '@/app/shared/services/form-toggle.service';

@Component({
  selector: 'fish-qualification-proof-toggle-box',
  imports: [
    IconCertificateComponent,
    IconLicenseCardComponent,
    SliderButtonComponent,
    SliderButtonGroupComponent,
    ToggleBoxComponent,
    ToggleBoxContentComponent,
    ToggleBoxHeaderComponent,
    ToggleBoxTabComponent,
    CertificateFormComponent,
    LicenseFormComponent,
    TranslateModule,
    IconQualificationsProofComponent,
    OtherQualificationsProofFormComponent,
  ],
  templateUrl: './qualification-proof-toggle-box.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class QualificationProofToggleBoxComponent extends FormComponent<QualificationProofFormGroup> implements AfterViewInit {
  // Constants
  public static readonly TAB_INDEX_LICENSE = 0;

  public static readonly TAB_INDEX_CERTIFICATE = 1;

  public static readonly TAB_INDEX_OTHER = 2;

  // Outputs
  @Output()
  public override readonly validated = new EventEmitter<boolean>();

  @Output()
  public readonly valueChanged = new EventEmitter<IQualificationProofToggleBoxValues>();

  @Output()
  public readonly tabChanged = new EventEmitter<number>();

  // Fields
  public override formGroup!: QualificationProofFormGroup;

  private licenseFormValues!: LicenseFormValues;

  private certificateFormValues!: CertificateFormValues;

  private otherQualificationsProofFormValues!: OtherQualificationsProofFormValues;

  private isCertificateFormValid: boolean = false;

  private isLicenseFormValid: boolean = false;

  private isOtherQualificationsProofFormValid: boolean = false;

  private readonly currentTabIndex = signal(0);

  @ViewChild(LicenseFormComponent)
  private readonly licenseForm!: LicenseFormComponent;

  @ViewChild(CertificateFormComponent)
  private readonly certificateForm!: CertificateFormComponent;

  @ViewChild(OtherQualificationsProofFormComponent)
  private readonly otherQualificationsProofForm!: OtherQualificationsProofFormComponent;

  // Dependencies
  private readonly formBuilder: FormBuilder = inject(FormBuilder);

  private readonly formToggleService: FormToggleService = inject(FormToggleService);

  public ngAfterViewInit(): void {
    this.formGroup = this.formBuilder.group({
      license: this.licenseForm.formGroup,
      certificate: this.certificateForm.formGroup,
      otherQualificationProof: this.otherQualificationsProofForm.formGroup,
      selectedQualification: this.formBuilder.nonNullable.control<'license' | 'certificate' | 'otherQualificationProof'>('license'),
    });

    this.formToggleService.updateFormValidation(this.formGroup, this.formGroup.controls.selectedQualification.value, ['selectedQualification']);

    this.formGroup.controls.selectedQualification.valueChanges.subscribe((value) => {
      this.formToggleService.updateFormValidation(this.formGroup, value, ['selectedQualification']);
    });
  }

  protected handleCertificateFormValidated(valid: boolean): void {
    this.isCertificateFormValid = valid;
    this.validated.emit(valid);
  }

  protected handleLicenseFormValidated(valid: boolean): void {
    this.isLicenseFormValid = valid;
    this.validated.emit(valid);
  }

  protected handleOtherQualificationsProofFormValidated(valid: boolean): void {
    this.isOtherQualificationsProofFormValid = valid;
    this.validated.emit(valid);
  }

  protected handleCertificateFormChanged(certificateFormValues: CertificateFormValues): void {
    this.certificateFormValues = certificateFormValues;
    this.valueChanged.emit({ certificateFormValues });
  }

  protected handleLicenseFormChanged(licenseFormValues: LicenseFormValues): void {
    this.licenseFormValues = licenseFormValues;
    this.valueChanged.emit({ licenseFormValues });
  }

  protected handleOtherQualificationsProofFormChanged(otherQualificationsProofFormValues: OtherQualificationsProofFormValues): void {
    this.otherQualificationsProofFormValues = otherQualificationsProofFormValues;
    this.valueChanged.emit({ otherQualificationsProofFormValues });
  }

  protected handleToggleBoxChanged(newIndex: number): void {
    this.tabChanged.emit(newIndex);
    this.currentTabIndex.set(newIndex);
    switch (newIndex) {
      case QualificationProofToggleBoxComponent.TAB_INDEX_LICENSE:
        this.updateSelectedTab({ licenseFormValues: this.licenseFormValues }, 'license', this.isLicenseFormValid);
        break;
      case QualificationProofToggleBoxComponent.TAB_INDEX_CERTIFICATE:
        this.updateSelectedTab({ certificateFormValues: this.certificateFormValues }, 'certificate', this.isCertificateFormValid);
        break;
      case QualificationProofToggleBoxComponent.TAB_INDEX_OTHER:
        this.updateSelectedTab(
          { otherQualificationsProofFormValues: this.otherQualificationsProofFormValues },
          'otherQualificationProof',
          this.isOtherQualificationsProofFormValid
        );
        break;
      default:
        throw new Error(`ToggleBox index out of bounds for index: ${newIndex}`);
    }
  }

  private updateSelectedTab(
    formValues: Partial<IQualificationProofToggleBoxValues>,
    selectedQualification: 'license' | 'certificate' | 'otherQualificationProof',
    isValid: boolean
  ): void {
    this.valueChanged.emit(formValues);
    this.formGroup.controls.selectedQualification.setValue(selectedQualification);
    this.validated.emit(isValid);
  }

  public override validate(): void {
    switch (this.currentTabIndex()) {
      case QualificationProofToggleBoxComponent.TAB_INDEX_LICENSE:
        this.licenseForm.validate();
        break;
      case QualificationProofToggleBoxComponent.TAB_INDEX_CERTIFICATE:
        this.certificateForm.validate();
        break;
      case QualificationProofToggleBoxComponent.TAB_INDEX_OTHER:
        this.otherQualificationsProofForm.validate();
        break;
      default:
        throw new Error(`ToggleBox index out of bounds for index: ${this.currentTabIndex()}`);
    }
  }
}
