package de.adesso.fischereiregister.core.utils;

import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.Person;

import java.util.Objects;

public class PersonUtils {
    private PersonUtils() {
    }

    /**
     * If the {@code birthname} attribute is not set on the provided {@code person} object, it is set to the current value of the {@code lastname} attribute.
     *
     * @param person The person to update the {@code birthname} value for.
     */
    public static void setBirthnameIfMissing(Person person) {
        if (person.getBirthname() == null || person.getBirthname().isEmpty()) {
            person.setBirthname(person.getLastname());
        }
    }

    public static String getFullName(Person person) {
        return String.join(" ",
                person.getTitle() != null ? person.getTitle() : "",
                person.getFirstname(),
                person.getLastname()
        ).trim();
    }

    /**
     * A person match is used to assume that the underlying persons are the same.
     * <p>
     * This is not the same as .equals since email address or other address data might change.
     * The following properties are considered:
     * <p>
     * title, firstname, lastname, birthname, birthdate, birthplace, nationality.
     * <p>
     * If the nationality is not set for either person, a match in nationality is assumed, since an unset nationality
     * is possible for initial certificate entries.
     */
    public static boolean matches(Person person1, Person person2) {
        final String birthname = getBirthName(person1);
        final String otherBirthname = getBirthName(person2);

        boolean matchingNationality = fieldMatches(person1.getNationality(), person2.getNationality());
        if (person2.getNationality() == null || person2.getNationality().isEmpty() ||
                person1.getNationality() == null || person1.getNationality().isEmpty()) {
            matchingNationality = true;
        }

        return fieldMatches(person1.getTitle(), person2.getTitle()) &&
                fieldMatches(person1.getFirstname(), person2.getFirstname()) &&
                fieldMatches(person1.getLastname(), person2.getLastname()) &&
                fieldMatches(birthname, otherBirthname) &&
                fieldMatches(person1.getBirthplace(), person2.getBirthplace()) &&
                fieldMatches(person1.getBirthdate(), person2.getBirthdate()) &&
                matchingNationality;
    }

    private static String getBirthName(Person person) {
        if (person.getBirthname() == null || person.getBirthname().isEmpty()) {
            return person.getLastname();
        }
        return person.getBirthname();
    }

    /**
     * Checks whether the values are the same but does some String normalization, i.e. will trim the string and null values
     * will match empty strings.
     */
    private static boolean fieldMatches(String value1, String value2) {
        final String normalizedValue1 = value1 == null ? "" : value1.trim();
        final String normalizedValue2 = value2 == null ? "" : value2.trim();

        return Objects.equals(normalizedValue1, normalizedValue2);
    }

    private static boolean fieldMatches(Birthdate value1, Birthdate value2) {
        return Objects.equals(value1, value2);
    }

    public static boolean unchangeableDataMatches(Person person, Person otherPerson) {
        final String birthname = PersonUtils.getBirthName(person);
        final String otherBirthname = PersonUtils.getBirthName(otherPerson);

        return fieldMatches(birthname, otherBirthname) &&
                fieldMatches(person.getBirthplace(), otherPerson.getBirthplace()) &&
                fieldMatches(person.getBirthdate(), otherPerson.getBirthdate());
    }
}
