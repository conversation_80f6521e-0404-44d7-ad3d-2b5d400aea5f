import { ChangeDetectionStrategy, Component, OnInit, inject } from '@angular/core';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';

import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Observable, forkJoin, map } from 'rxjs';

import { UserService } from '@/app/core/services/user/user.service';
import { NoPermanentResidenceFormGroup } from '@/app/features/register-edit/components/organisms/no-permanent-residence-form/no-permanent-residence-form.models';
import { FormComponent } from '@/app/shared/atoms/form/form.component';
import { FishBeforeUnloadAndCanDeactivate } from '@/app/shared/decorators/before-unload-and-can-deactivate/before-unload-and-can-deactivate.decorator';
import { DIN91379_REGEXES } from '@/app/shared/models/constants';
import { FormFieldComponent } from '@/app/shared/organisms/form-field/form-field.component';
import { ValidationErrorMapping } from '@/app/shared/organisms/form-field/form-field.models';

@Component({
  selector: 'fish-no-permanent-residence-form',
  imports: [FormFieldComponent, TranslateModule, ReactiveFormsModule],
  templateUrl: './no-permanent-residence-form.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
@FishBeforeUnloadAndCanDeactivate()
export class NoPermanentResidenceFormComponent extends FormComponent<NoPermanentResidenceFormGroup> implements OnInit {
  // Fields
  public override formGroup!: NoPermanentResidenceFormGroup;

  protected postcodeErrorMapping$!: Observable<ValidationErrorMapping>;

  // Dependencies
  private readonly formBuilder: FormBuilder = inject(FormBuilder);

  private readonly translate: TranslateService = inject(TranslateService);

  private readonly user: UserService = inject(UserService);

  constructor() {
    super();
    this.initFormGroup();
    this.captureInitialState();
  }

  public override ngOnInit(): void {
    super.ngOnInit();
    this.initErrorMappings();
    this.initOfficeAddress();
  }

  private initFormGroup(): void {
    this.formGroup = this.formBuilder.group({
      office: this.formBuilder.control(this.defaultValues?.office ?? '', [
        Validators.required,
        Validators.pattern(DIN91379_REGEXES.GROUP_B_GENERAL_NAMES),
      ]),
      deliverTo: this.formBuilder.control(this.defaultValues?.deliverTo ?? '', [Validators.pattern(DIN91379_REGEXES.GROUP_A_PERSONS)]),
      city: this.formBuilder.control(this.defaultValues?.city ?? '', [
        Validators.required,
        Validators.pattern(DIN91379_REGEXES.GROUP_B_GENERAL_NAMES),
      ]),
      street: this.formBuilder.control(this.defaultValues?.street ?? '', [
        Validators.required,
        Validators.pattern(DIN91379_REGEXES.GROUP_B_GENERAL_NAMES),
      ]),
      streetNumber: this.formBuilder.control(this.defaultValues?.streetNumber ?? '', [
        Validators.required,
        Validators.pattern(DIN91379_REGEXES.GROUP_B_GENERAL_NAMES),
      ]),
      postcode: this.formBuilder.control(this.defaultValues?.postcode ?? '', [Validators.required, Validators.pattern('[0-9]*')]),
      detail: this.formBuilder.control(this.defaultValues?.detail ?? '', [Validators.pattern(DIN91379_REGEXES.GROUP_B_GENERAL_NAMES)]),
    });
  }

  private initErrorMappings(): void {
    this.postcodeErrorMapping$ = forkJoin([
      this.translate.get('common.form.error.postcode.pattern'),
      this.translate.get('common.form.error.postcode.min_length'),
      this.translate.get('common.form.error.postcode.max_length'),
    ]).pipe(
      map(([patternError, minLengthError, maxLengthError]) => ({
        pattern: patternError,
        minLength: minLengthError,
        maxLengthError: maxLengthError,
      }))
    );
  }

  private initOfficeAddress(): void {
    this.user.getOfficeAddress$().subscribe((address) => {
      this.formGroup.patchValue(address);
      this.captureInitialState();
    });
  }
}
