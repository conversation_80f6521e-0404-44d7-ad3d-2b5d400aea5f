import { AfterViewInit, ChangeDetectionStrategy, Component, EventEmitter, Output, ViewChild, inject, signal } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { BehaviorSubject } from 'rxjs';

import { CertificateFormGroup } from '@/app/features/register-edit/components/organisms/certificate-form/certificate-form.model';
import { EditFooterComponent } from '@/app/features/register-edit/components/organisms/edit-footer/edit-footer.component';
import { LicenseFormGroup } from '@/app/features/register-edit/components/organisms/license-form/license-form.models';
import { OtherQualificationsProofFormGroup } from '@/app/features/register-edit/components/organisms/other-qualifications-proof-form/other-qualifications-proof-form.model';
import { QualificationProofLinkboxComponent } from '@/app/features/register-edit/components/organisms/qualification-proof-linkbox/qualification-proof-linkbox.component';
import { QualificationProofOtherInfoboxComponent } from '@/app/features/register-edit/components/organisms/qualification-proof-other-infobox/qualification-proof-other-infobox.component';
import { QualificationProofToggleBoxComponent } from '@/app/features/register-edit/components/organisms/qualification-proof-toggle-box/qualification-proof-toggle-box.component';
import { IQualificationProofToggleBoxValues } from '@/app/features/register-edit/components/organisms/qualification-proof-toggle-box/qualification-proof-toggle-box.model';
import { QualificationProofFormGroup } from '@/app/features/register-edit/components/organisms/steps/qualification-proof-step/qualification-proof-step.models';
import { EditFormStep } from '@/app/features/register-edit/interfaces/edit-form-step';
import { FederalState } from '@/app/shared/models/federal-state';
import { DataCatalogService } from '@/app/shared/services/data-catalog.service';

@Component({
  selector: 'fish-qualification-proof-step',
  imports: [
    EditFooterComponent,
    TranslateModule,
    QualificationProofToggleBoxComponent,
    QualificationProofLinkboxComponent,
    QualificationProofOtherInfoboxComponent,
  ],
  templateUrl: './qualification-proof-step.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class QualificationProofStepComponent implements AfterViewInit, EditFormStep<QualificationProofFormGroup> {
  // Dependencies
  private readonly dataCatalogService: DataCatalogService = inject(DataCatalogService);

  // Outputs
  @Output()
  public readonly backButtonClicked = new EventEmitter<void>();

  @Output()
  public readonly continueButtonClicked = new EventEmitter<void>();

  // Fields
  public canContinue$ = new BehaviorSubject<boolean>(false);

  public formGroup!: QualificationProofFormGroup;

  protected selectedFederalStateId: string = '';

  protected currentTabIndex = signal(0);

  protected readonly QualificationProofToggleBoxComponent = QualificationProofToggleBoxComponent;

  private federalStates!: FederalState[];

  private selectedFederalState: string = '';

  @ViewChild(QualificationProofToggleBoxComponent)
  private readonly qualificationProofToggleBox!: QualificationProofToggleBoxComponent;

  public ngAfterViewInit(): void {
    this.formGroup = this.qualificationProofToggleBox.formGroup;
    this.initFederalStates();
  }

  private initFederalStates(): void {
    this.dataCatalogService.getFederalStates$().subscribe((federalStates) => {
      this.federalStates = federalStates;
    });
  }

  protected handleToggleBoxTabChanged(index: number): void {
    this.currentTabIndex.set(index);
  }

  protected handleToggleBoxValidated(valid: boolean): void {
    this.canContinue$.next(valid);
  }

  protected handleToggleBoxValuesChanged(newToggleBoxValues: IQualificationProofToggleBoxValues): void {
    const { licenseFormValues, certificateFormValues, otherQualificationsProofFormValues } = newToggleBoxValues;

    // Helper function to get federal state value
    const getFederalStateValue = (formGroup: LicenseFormGroup | CertificateFormGroup | OtherQualificationsProofFormGroup) => {
      const federalStateControl = formGroup.controls.federalState;
      return !federalStateControl.invalid ? federalStateControl.value! : '';
    };

    if (licenseFormValues || certificateFormValues || otherQualificationsProofFormValues) {
      if (licenseFormValues) {
        this.selectedFederalState = getFederalStateValue(this.formGroup.controls.license);
      }
      if (certificateFormValues) {
        this.selectedFederalState = getFederalStateValue(this.formGroup.controls.certificate);
      }
      if (otherQualificationsProofFormValues) {
        this.selectedFederalState = getFederalStateValue(this.formGroup.controls.otherQualificationProof);
      }
    } else {
      this.selectedFederalState = '';
    }
    this.updateSelectedFederalStateId();
  }

  private updateSelectedFederalStateId() {
    const selectedFederalState: FederalState = this.federalStates.find((federalState) => federalState.name === this.selectedFederalState)!;
    this.selectedFederalStateId = selectedFederalState?.id ?? '';
  }

  protected onContinue(): void {
    this.qualificationProofToggleBox.validate();
    if (this.formGroup.valid) {
      this.continueButtonClicked.emit();
    }
  }
}
