import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { KeycloakService } from 'keycloak-angular';
import { Observable } from 'rxjs';

import { TemplateSubstitutionService } from '@/app/core/services/template-substitution.service';
import { UserRole } from '@/app/core/services/user/user.constants';
import { UserService } from '@/app/core/services/user/user.service';
import { UserDropdownMenuComponent } from '@/app/shared/molecules/user-dropdown-menu/user-dropdown-menu.component';

@Component({
  selector: 'fish-user-info',
  imports: [TranslateModule, CommonModule, UserDropdownMenuComponent],
  providers: [TemplateSubstitutionService],
  templateUrl: './user-info.component.html',
})
export class UserInfoComponent {
  protected userName$: Observable<string>;

  protected affiliation$: Observable<string>;

  constructor(
    private readonly userService: UserService,
    private readonly keycloak: KeycloakService
  ) {
    this.userName$ = userService.getDisplayName$();
    this.affiliation$ = keycloak.isUserInRole(UserRole.Official) ? userService.getGovernmentOffice$() : userService.getExaminationIssuer$();
  }
}
