import { ChangeDetectionStrategy, Component, computed, inject, signal } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { of } from 'rxjs';

import { LicenseType, StatisticsService } from '@digifischdok/ngx-register-sdk';

import { CertificationsStatisticsReportTileComponent } from '@/app/features/reportings/components/organisms/certifications-statistics-report-tile/certifications-statistics-report-tile.component';
import { LicensesStatisticsReportTileComponent } from '@/app/features/reportings/components/organisms/licenses-statistics-report-tile/licenses-statistics-report-tile.component';
import { StatisticsDashboardFilterComponent } from '@/app/features/reportings/components/organisms/statistics-dashboard-filter/statistics-dashboard-filter.component';
import {
  ALL_CERTIFICATE_ISSUERS_FILTER_OPTION,
  ALL_OFFICES_FILTER_OPTION,
  StatisticsDashboardFilterData,
  StatisticsDashboardFilterType,
} from '@/app/features/reportings/components/organisms/statistics-dashboard-filter/statistics-dashboard-filter.models';
import { TaxesStatisticsReportTileComponent } from '@/app/features/reportings/components/organisms/taxes-statistics-report-tile/taxes-statistics-report-tile.component';
import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { IconDownloadComponent } from '@/app/shared/icons/download/download.component';
import { numberRange } from '@/app/shared/utils/array.utils';
import { toComputed } from '@/app/shared/utils/rxJsInterop.utils';

@Component({
  selector: 'fish-statistics-dashboard',
  standalone: true,
  imports: [
    ButtonComponent,
    IconDownloadComponent,
    StatisticsDashboardFilterComponent,
    TaxesStatisticsReportTileComponent,
    LicensesStatisticsReportTileComponent,
    CertificationsStatisticsReportTileComponent,
    TranslateModule,
  ],
  templateUrl: './statistics-dashboard.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StatisticsDashboardComponent {
  // Constants
  protected readonly StatisticsDashboardFilterType = StatisticsDashboardFilterType;

  protected readonly LicenseType = LicenseType;

  private readonly YEARS_TO_QUERY_AMOUNT = 6;

  // Dependencies
  private readonly statisticsService = inject(StatisticsService);

  // Signals
  protected readonly filterData = signal<StatisticsDashboardFilterData | null>(null);

  protected readonly typeFilter = computed(() => this.filterData()?.type);

  private readonly yearFilter = computed(() => this.filterData()?.year);

  private readonly federalStateFilter = computed(() => this.filterData()?.federalState);

  private readonly officeFilter = computed(() => this.filterData()?.office);

  private readonly certificateIssuerFilter = computed(() => this.filterData()?.certificateIssuer);

  protected readonly taxesStatistics = toComputed(() => {
    const year = this.yearFilter();
    const state = this.federalStateFilter();
    const office = this.officeFilter();

    if (!year || !state) {
      return of([]);
    }

    const yearsToQuery = this.generateYearsToQuery(year);
    const officeParam = office && office !== ALL_OFFICES_FILTER_OPTION ? office : undefined;

    return this.statisticsService.statisticsControllerGetTaxesStatistics(yearsToQuery, officeParam, state);
  });

  protected readonly regularLicensesStatistics = toComputed(() => {
    const year = this.yearFilter();
    const state = this.federalStateFilter();
    const office = this.officeFilter();

    if (!year || !state) {
      return of([]);
    }

    const yearsToQuery = this.generateYearsToQuery(year);
    const officeParam = office && office !== ALL_OFFICES_FILTER_OPTION ? office : undefined;

    return this.statisticsService.statisticsControllerGetRegularLicensesStatistics(yearsToQuery, officeParam, state);
  });

  protected readonly vacationLicensesStatistics = toComputed(() => {
    const year = this.yearFilter();
    const state = this.federalStateFilter();
    const office = this.officeFilter();

    if (!year || !state) {
      return of([]);
    }

    const yearsToQuery = this.generateYearsToQuery(year);
    const officeParam = office && office !== ALL_OFFICES_FILTER_OPTION ? office : undefined;

    return this.statisticsService.statisticsControllerGetVacationLicensesStatistics(yearsToQuery, officeParam, state);
  });

  protected readonly limitedLicensesStatistics = toComputed(() => {
    const year = this.yearFilter();
    const state = this.federalStateFilter();
    const office = this.officeFilter();

    if (!year || !state) {
      return of([]);
    }

    const yearsToQuery = this.generateYearsToQuery(year);
    const officeParam = office && office !== ALL_OFFICES_FILTER_OPTION ? office : undefined;

    return this.statisticsService.statisticsControllerGetLimitedLicensesStatistics(yearsToQuery, officeParam, state);
  });

  protected readonly certificationsStatistics = toComputed(() => {
    const year = this.yearFilter();
    const state = this.federalStateFilter();
    const certificateIssuer = this.certificateIssuerFilter();

    if (!year || !state) {
      return of([]);
    }

    const yearsToQuery = this.generateYearsToQuery(year);
    const certificateIssuerParam = certificateIssuer && certificateIssuer !== ALL_CERTIFICATE_ISSUERS_FILTER_OPTION ? certificateIssuer : undefined;

    return this.statisticsService.statisticsControllerGetCertificationsStatistics(yearsToQuery, certificateIssuerParam, state);
  });

  private generateYearsToQuery(year: string): number[] {
    const startYear = Number(year);
    const endYear = startYear - this.YEARS_TO_QUERY_AMOUNT + 1;
    return numberRange(startYear, endYear);
  }
}
