import { ChangeDetectionStrategy, Component, computed, inject, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { KeycloakService } from 'keycloak-angular';

import { UserRole } from '@/app/core/services/user/user.constants';
import { ButtonMenuContentComponent } from '@/app/shared/atoms/button-menu-content/button-menu-content.component';
import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { IconDeleteComponent } from '@/app/shared/icons/delete/delete.component';
import { IconLockComponent } from '@/app/shared/icons/lock/lock.component';
import { ButtonMenuComponent } from '@/app/shared/molecules/button-menu/button-menu.component';

@Component({
  selector: 'fish-interaction-menu',
  imports: [ButtonComponent, ButtonMenuComponent, ButtonMenuContentComponent, IconLockComponent, TranslateModule, IconDeleteComponent],
  templateUrl: './interaction-menu.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InteractionMenuComponent {
  public readonly isPersonBanned = input.required<boolean>();

  public readonly isJurisdictionMatching = input.required<boolean>();

  protected readonly showBanButton = computed<boolean>(() => {
    const hasRole = this.keycloak.getKeycloakInstance().hasRealmRole(UserRole.BanManager);
    return hasRole && this.isJurisdictionMatching();
  });

  protected readonly showDeleteButton = computed<boolean>(() => {
    const hasRole = this.keycloak.getKeycloakInstance().hasRealmRole(UserRole.DeletionManager);
    return hasRole && this.isJurisdictionMatching();
  });

  protected readonly showMenu = computed<boolean>(() => this.showBanButton() || this.showDeleteButton());

  // dependencies
  private readonly keycloak = inject(KeycloakService);
}
