<fish-focus-ring class="h-16 w-16">
  <button
    [class]="classes()"
    cdkOverlayOrigin
    #overlayOrigin="cdkOverlayOrigin"
    #toggleButton
    [attr.aria-label]="'header.user.menu_button_label' | translate"
    [attr.aria-expanded]="menuIsOpen()"
    [attr.aria-haspopup]="'menu'"
    (click)="toggleMenu()"
    (focus)="onToggleButtonFocus($event)"
    (blur)="onToggleButtonBlur($event)"
    type="button"
  >
    <div class="absolute inset-0 flex items-center justify-center">
      <fish-profile-icon />
    </div>
  </button>
</fish-focus-ring>

<ng-template
  cdkConnectedOverlay
  [cdkConnectedOverlayOrigin]="overlayOrigin"
  [cdkConnectedOverlayOpen]="menuIsOpen()"
  [cdkConnectedOverlayPositions]="[
    {
      originX: 'end',
      originY: 'bottom',
      overlayX: 'end',
      overlayY: 'top',
      offsetY: 8,
    },
    {
      originX: 'end',
      originY: 'top',
      overlayX: 'end',
      overlayY: 'bottom',
      offsetY: -8,
    },
  ]"
  [cdkConnectedOverlayHasBackdrop]="true"
  (backdropClick)="closeMenu()"
  cdkConnectedOverlayBackdropClass="bg-transparent"
  [cdkConnectedOverlayScrollStrategy]="scrollStrategy"
  [cdkConnectedOverlayDisposeOnNavigation]="true"
  [cdkConnectedOverlayFlexibleDimensions]="false"
  [cdkConnectedOverlayGrowAfterOpen]="false"
  [cdkConnectedOverlayPush]="false"
>
  <div @dropdownOverlayAnimation role="menu">
    <fish-button type="secondary" size="l" (clicked)="handleLogout()" #logoutButton data-testid="logout-button">
      <fish-icon-signout icon size="48" />
      <span [innerText]="'header.user.logout_button_label' | translate"></span>
    </fish-button>
  </div>
</ng-template>
