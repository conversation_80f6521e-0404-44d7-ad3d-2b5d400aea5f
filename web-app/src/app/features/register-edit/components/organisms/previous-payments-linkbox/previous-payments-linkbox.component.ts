import { Async<PERSON>ip<PERSON>, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { LinkboxLinkComponent } from '@/app/shared/atoms/linkbox-link/linkbox-link.component';
import { IconDocumentPdfComponent } from '@/app/shared/icons/document-pdf/document-pdf.component';
import { IconLinkComponent } from '@/app/shared/icons/link/link.component';
import { LinkboxComponent } from '@/app/shared/molecules/linkbox/linkbox.component';
import { ToSecureFileUrlPipe } from '@/app/shared/pipes/to-secure-file-url.pipe';

@Component({
  selector: 'fish-previous-payments-linkbox',
  imports: [
    LinkboxComponent,
    LinkboxLinkComponent,
    IconLinkComponent,
    IconDocumentPdfComponent,
    TranslateModule,
    NgIf,
    ToSecureFileUrlPipe,
    AsyncPipe,
  ],
  templateUrl: './previous-payments-linkbox.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PreviousPaymentsLinkboxComponent {
  @Input({ required: true }) public federalStateId!: string;
}
