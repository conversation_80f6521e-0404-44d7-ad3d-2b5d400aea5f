package de.adesso.fischereiregister.protocol.service;

import de.adesso.fischereiregister.protocol.persistence.InspectorProtocol;
import de.adesso.fischereiregister.protocol.persistence.InspectorProtocolRepository;
import de.adesso.fischereiregister.protocol.persistence.result.model.ActiveInspectorsResult;
import de.adesso.fischereiregister.protocol.persistence.result.model.NumberOfInspectionsResult;
import de.adesso.fischereiregister.protocol.service.model.InspectionsStatisticsResult;
import de.adesso.fischereiregister.protocol.util.InspectionsStatisticsMapperUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
@AllArgsConstructor
public class InspectorProtocolServiceImpl implements InspectorProtocolService {
    private final InspectorProtocolRepository inspectorProtocolRepository;

    @Override
    public void createProtocolEntry(String inspectorUserId, String registerEntryId, String inspectorFederalState, LocalDateTime inspectionTimestamp) {

        InspectorProtocol inspectorProtocol = new InspectorProtocol();
        inspectorProtocol.setInspectorUserId(inspectorUserId);
        inspectorProtocol.setRegisterEntryId(registerEntryId);
        inspectorProtocol.setInspectorFederalState(inspectorFederalState);
        inspectorProtocol.setInspectionTimestamp(inspectionTimestamp);

        inspectorProtocolRepository.save(inspectorProtocol);
    }

    @Override
    public List<InspectionsStatisticsResult> getInspectionsStatistics(List<Integer> years, String federalState) {
        List<Integer> yearsForSelect = years == null || years.isEmpty() ? null : years;
        List<ActiveInspectorsResult> activeInspectorsResults = inspectorProtocolRepository.selectActiveInspectors(yearsForSelect, federalState);
        List<NumberOfInspectionsResult> numberOfInspectionsResults = inspectorProtocolRepository.selectNumberOfInspections(yearsForSelect, federalState);

        return InspectionsStatisticsMapperUtil.mapToInspectionsStatistics(activeInspectorsResults, numberOfInspectionsResults);
    }

}