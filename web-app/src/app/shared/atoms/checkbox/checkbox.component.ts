import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';

import { map } from 'rxjs';
import { twMerge } from 'tailwind-merge';

import { AutoGeneratedIdComponent } from '@/app/shared/atoms/auto-generated-id-component/auto-generated-id.component';
import { checkboxVariants } from '@/app/shared/atoms/checkbox/checkbox.component.styles';
import { FocusRingComponent } from '@/app/shared/atoms/focus-ring/focus-ring.component';
import { IconCheckComponent } from '@/app/shared/icons/check/check.component';
import { toComputed } from '@/app/shared/utils/rxJsInterop.utils';

@Component({
  selector: 'fish-checkbox',
  templateUrl: './checkbox.component.html',
  imports: [ReactiveFormsModule, IconCheckComponent, FocusRingComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CheckboxComponent extends AutoGeneratedIdComponent {
  public readonly externalFocusRing = input<boolean>();

  public readonly formControl = input<FormControl<boolean | null>>(new FormControl(false));

  public readonly class = input<string>();

  protected labelId = `${this.id}-label`;

  protected labelClasses = computed(() => twMerge('relative', 'flex', 'items-center', 'gap-2', this.class()));

  private readonly invalid = toComputed(() =>
    this.formControl().statusChanges.pipe(map(() => this.formControl().invalid && this.formControl().touched))
  );

  protected checkBoxClasses = computed(() => twMerge(checkboxVariants({ invalid: this.invalid() })));
}
