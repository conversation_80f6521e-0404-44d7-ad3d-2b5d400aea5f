<div class="flex flex-row gap-4 text-xs">
  <fish-form-field
    class="w-[266px]"
    size="small"
    type="combobox"
    [options]="typeComboboxOptions"
    [control]="typeControl"
    [label]="'statistics.dashboard.filter.type' | translate"
    [showErrorLabel]="false"
  />
  <fish-form-field
    class="w-[100px]"
    size="small"
    type="combobox"
    [options]="yearComboboxOptions"
    [control]="yearControl"
    [label]="'statistics.dashboard.filter.year' | translate"
    [showErrorLabel]="false"
  />
  <fish-form-field
    class="w-[80px]"
    size="small"
    type="combobox"
    [options]="federalStateComboboxOptions"
    [control]="federalStateControl"
    [label]="'statistics.dashboard.filter.federal_state' | translate"
    [showErrorLabel]="false"
  />
  @if (isLicensesAndTaxesSelected()) {
    <div class="h-14 w-px bg-border-divider-alternative" @fadeIn></div>

    <fish-form-field
      @fadeIn
      class="w-[200px]"
      size="small"
      type="combobox"
      [options]="officeFilterOptions()"
      [control]="officeControl"
      [label]="'statistics.dashboard.filter.offices' | translate"
      [showErrorLabel]="false"
    />
  }
  @if (isCertificationsSelected()) {
    <div class="h-14 w-px bg-border-divider-alternative" @fadeIn></div>

    <fish-form-field
      @fadeIn
      class="w-[200px]"
      size="small"
      type="combobox"
      [options]="certificateIssuerFilterOptions()"
      [control]="certificateIssuerControl"
      [label]="'statistics.dashboard.filter.certificate_issuers' | translate"
      [showErrorLabel]="false"
    />
  }
</div>
