import { HttpClient } from '@angular/common/http';
import { Injectable, OnDestroy, SecurityContext } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';

import { Observable, map, of, shareReplay } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class FileDownloadService implements OnDestroy {
  private readonly cache = new Map<string, string>();

  private readonly blobUrls = new Set<string>();

  constructor(
    private readonly http: HttpClient,
    private readonly sanitizer: DomSanitizer
  ) {}

  public getObjectUrl$(src: string): Observable<string | undefined> {
    if (this.cache.has(src)) {
      return of(this.cache.get(src));
    }

    return this.http.get(src, { responseType: 'blob', observe: 'response' }).pipe(
      map((response) => {
        const blob = response.body;
        const contentType = response.headers.get('content-type') || 'application/octet-stream';
        if (!blob) return;
        const fileBlob = new Blob([blob], { type: contentType });
        const url = URL.createObjectURL(fileBlob);
        const sanitizedUrl = this.sanitizer.sanitize(SecurityContext.URL, url);
        if (sanitizedUrl) {
          this.cache.set(src, sanitizedUrl);
        }
        return sanitizedUrl || undefined;
      }),
      shareReplay({ bufferSize: 1, refCount: true })
    );
  }

  public ngOnDestroy(): void {
    this.blobUrls.forEach((url) => URL.revokeObjectURL(url));
    this.blobUrls.clear();
    this.cache.clear();
  }
}
