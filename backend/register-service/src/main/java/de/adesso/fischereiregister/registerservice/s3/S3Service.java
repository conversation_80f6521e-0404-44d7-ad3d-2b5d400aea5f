package de.adesso.fischereiregister.registerservice.s3;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

@Service
public class S3Service {

    private final AmazonS3 amazonS3;

    @Value("${spring.cloud.aws.s3.bucket}")
    private String bucketName;

    public S3Service(AmazonS3 amazonS3) {
        this.amazonS3 = amazonS3;
    }

    public S3ObjectResponse downloadFile(String key) {
        S3Object s3Object = amazonS3.getObject(bucketName, key);
        String contentType = s3Object.getObjectMetadata().getContentType();

        try (S3ObjectInputStream inputStream = s3Object.getObjectContent();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            byte[] content = outputStream.toByteArray();

            return new S3ObjectResponse(
                    content,
                    contentType
            );
        } catch (IOException e) {
            throw new RuntimeException("Failed to download file from S3: " + key, e);
        }
    }
}


