package de.adesso.fischereiregister.registerservice.fishing_license_export;


import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.ExportType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.UUID;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
public class FishingLicenseExportIntegrationTest {


    @Autowired
    private MockMvc mvc;


    @Test
    @DisplayName("""
            	GET /api/register-entries/{registerId}/identification-documents/{documentId}/pdf?type=FISHING_LICENSE
            	Verify that the PDF for FISHING_LICENSE is exported successfully.
            """)
    void testExportDigitizedFishingCertificateSuccess() throws Exception {
        final String documentId = "401be53c-a1d4-4600-bdc0-fe0d9772539b";
        final UUID registerId = UUID.fromString("351031b4-d8ca-4380-a49f-47159ac68b8b");

        mvc.perform(MockMvcRequestBuilders
                        .get("http://localhost:8080/register-entries/" + registerId + "/identification-documents/"
                                + documentId
                                + "/pdf?type=" + ExportType.FISHING_LICENSE.name())
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_PDF))
                .andExpect(status().isOk());

    }

    @Test
    @DisplayName("""
            	GET /api/register-entries/{registerId}/identification-documents/{documentId}/pdf?type=FISHING_TAXES
            	Verify that the PDF for FISHING_TAXES is exported successfully.
            """)
    void testExportDigitizedFishingTaxSuccess() throws Exception {
        final String documentId = "a7f405d1-fcbf-4505-8303-47b14b5b8c80";
        final UUID registerId = UUID.fromString("3049614a-61d9-4e14-b7cb-38ac25bf4d4c");

        mvc.perform(MockMvcRequestBuilders
                        .get("http://localhost:8080/register-entries/" + registerId +
                                "/identification-documents/" + documentId +
                                "/pdf?type=" + ExportType.FISHING_TAXES.name())
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_PDF))
                .andExpect(status().isOk());


    }

    @Test
    @DisplayName("""
            	GET /api/register-entries/{registerId}/identification-documents/{documentId}/pdf?type=FISHING_TAXES
            	Verify that the PDF for FISHING_TAXES is exported successfully.
            """)
    void testExportPDFFailsDocumentFoundButNoTaxesInform() throws Exception {
        final String documentId = "54cb8382-dac7-4bb9-8684-57172619eb18";
        final UUID registerId = UUID.fromString("3049614a-61d9-4e14-b7cb-38ac25bf4d4c");

        mvc.perform(MockMvcRequestBuilders
                        .get("http://localhost:8080/register-entries/" + registerId +
                                "/identification-documents/" + documentId +
                                "/pdf?type=" + ExportType.FISHING_TAXES.name())
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_PDF))
                .andExpect(status().isNotFound());

    }


    @Test
    @DisplayName("""
            	GET /api/register-entries/{registerId}/identification-documents/{documentId}/pdf
            	Verify that the API returns a not found status code for not existing ids.
            """)
    void testExportPDFFailsWithEntityNotFoundException() throws Exception {

        final UUID registerId = UUID.randomUUID();
        final String documentId = UUID.randomUUID().toString();

        mvc.perform(get(
                        "http://localhost:8080/register-entries/" + registerId +
                                "/identification-documents/" + documentId +
                                "/pdf?type=" + ExportType.FISHING_LICENSE.name()))
                .andExpect(status().isNotFound());
    }
}
