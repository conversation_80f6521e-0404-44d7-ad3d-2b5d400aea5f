package de.adesso.fischereiregister.registerservice.fishing_license_export.port;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.type.FederalState;

import java.math.BigDecimal;

public interface TenantRulesValuesPort {

    BigDecimal retrieveFeeAnalog(FederalState federalState) throws RulesProcessingException;

    BigDecimal retrieveFeeDigital(FederalState federalState) throws RulesProcessingException;
}
