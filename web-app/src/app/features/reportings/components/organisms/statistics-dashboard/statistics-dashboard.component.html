<div class="flex max-h-[100%] w-full flex-col divide-y divide-border-divider-alternative" data-testid="statistics-dashboard">
  <!-- Start Upper part -->
  <div class="flex min-h-[5.5rem] w-full flex-row items-center px-4 py-2">
    <fish-statistics-dashboard-filter (filterChange)="filterData.set($event)"></fish-statistics-dashboard-filter>
    <div class="grow"></div>
    <div class="flex">
      <fish-button>
        <fish-icon-download icon size="32"></fish-icon-download>
        <p class="text-left" [innerHTML]="'statistics.dashboard.export_button' | translate"></p>
      </fish-button>
    </div>
  </div>
  <!-- End Upper part -->
  <!-- Start Lower part -->
  <div class="overflow-y-scroll p-4">
    <div class="sm:grid-cols-2 grid w-full grid-cols-1 gap-4 lg:grid-cols-3">
      @if (typeFilter() === StatisticsDashboardFilterType.ALL || typeFilter() === StatisticsDashboardFilterType.LICENSES_AND_TAXES) {
        <fish-taxes-statistics-report-tile
          data-testid="taxes-statistics-report-tile"
          [statistics]="taxesStatistics()"
        ></fish-taxes-statistics-report-tile>
      }
      @if (typeFilter() === StatisticsDashboardFilterType.ALL || typeFilter() === StatisticsDashboardFilterType.LICENSES_AND_TAXES) {
        <fish-licenses-statistics-report-tile
          data-testid="regular-licenses-statistics-report-tile"
          [statistics]="regularLicensesStatistics()"
          [licenseType]="LicenseType.Regular"
        ></fish-licenses-statistics-report-tile>
      }
      @if (typeFilter() === StatisticsDashboardFilterType.ALL || typeFilter() === StatisticsDashboardFilterType.LICENSES_AND_TAXES) {
        <fish-licenses-statistics-report-tile
          data-testid="vacation-licenses-statistics-report-tile"
          [statistics]="vacationLicensesStatistics()"
          [licenseType]="LicenseType.Vacation"
        ></fish-licenses-statistics-report-tile>
      }
      @if (typeFilter() === StatisticsDashboardFilterType.ALL || typeFilter() === StatisticsDashboardFilterType.LICENSES_AND_TAXES) {
        <fish-licenses-statistics-report-tile
          data-testid="limited-licenses-statistics-report-tile"
          [statistics]="limitedLicensesStatistics()"
          [licenseType]="LicenseType.Limited"
        ></fish-licenses-statistics-report-tile>
      }
      @if (typeFilter() === StatisticsDashboardFilterType.ALL || typeFilter() === StatisticsDashboardFilterType.CERTIFICATIONS) {
        <fish-certifications-statistics-report-tile
          data-testid="certifications-statistics-report-tile"
          [statistics]="certificationsStatistics()"
        ></fish-certifications-statistics-report-tile>
      }
    </div>
  </div>
  <!-- End Lower part -->
</div>
