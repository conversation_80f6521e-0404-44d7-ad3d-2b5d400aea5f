import { cva } from 'class-variance-authority';

export const confirmBoxStyles = cva(
  ['group', 'flex', 'h-full', 'items-center', 'gap-4', 'rounded', 'pl-6', 'pr-8', 'backdrop-blur-xl', 'transition-all', 'duration-240', 'ease-out'],
  {
    variants: {
      variant: {
        primary: [
          'bg-noticearea-fill',
          'shadow-glass-white-tint',
          'has-[:checked]:bg-noticearea-fill-checked',
          'has-[:checked]:shadow-glass-blue-tint',
        ],
        error: ['bg-feedback-background-warning-2', 'shadow-glass-red-tint'],
      },
    },
    defaultVariants: {
      variant: 'primary',
    },
  }
);
