import { ChangeDetectionStrategy, Component } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { LinkboxLinkComponent } from '@/app/shared/atoms/linkbox-link/linkbox-link.component';
import { IconLinkComponent } from '@/app/shared/icons/link/link.component';
import { IconPrintComponent } from '@/app/shared/icons/print/print.component';
import { LinkboxComponent } from '@/app/shared/molecules/linkbox/linkbox.component';

@Component({
  selector: 'fish-consent-linkbox',
  imports: [LinkboxComponent, LinkboxLinkComponent, TranslateModule, IconLinkComponent, IconPrintComponent],
  templateUrl: './consent-linkbox.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ConsentLinkboxComponent {}
