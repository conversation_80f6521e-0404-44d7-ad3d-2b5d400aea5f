import "./SigningEmployee.tsp";

@doc("Information registered by the government official signing and documenting the process of application for a limited license.")
model LimitedLicenseApproval {

    @doc("Contains information about the goverment official who processed the limited license application.")
    signingEmployee: SigningEmployee;

    @doc("Creation date of the license approval for the limited license.")
    createdAt: string;

    @doc("Identifier for the license Approval")
    fileNumber: string;

    @doc("Used to inform about payment information on the license approval document.")
    cashRegisterSign: string;

    @doc("Notice with the reason for the limited duration of the limited license.")
    justificationForLimitedDurationNotice?: string;

}
