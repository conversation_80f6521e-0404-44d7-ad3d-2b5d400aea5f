<div
  class="backdrop glass absolute inset-0 z-[-1] rounded-lg border-b-4 border-border-divider-white bg-tint-white opacity-90 shadow-glass-white-tint backdrop-blur-md"
></div>

<div class="absolute inset-0 box-content border-b-4 border-transparent" data-testid="reportings-dashboard">
  <div class="flex h-full w-full flex-row items-stretch divide-x divide-border-divider-alternative">
    <!-- Start Right part   -->
    <div class="flex flex-col divide-y divide-border-divider-alternative">
      <!-- Start Upper Left part -->
      <div class="flex min-h-[5.5rem] flex-row items-center gap-2 px-4 py-2 text-action-primary">
        <fish-icon-statistic size="48" />
        <span class="title-shadow font-bold" [innerText]="'reportings.title' | translate"></span>
      </div>
      <!-- End Upper Left part -->

      <!-- Start Lower Left part -->
      <div class="p-4">
        <fish-reportings-slider-button (changed)="handleReportingsSliderButtonChanged($event)"></fish-reportings-slider-button>
      </div>
      <!-- End Lower Left part -->
    </div>
    <!-- End Left part   -->

    <!-- Start Right part   -->
    @if (dashboardType() === 'statistics') {
      <fish-statistics-dashboard class="w-full"></fish-statistics-dashboard>
    } @else if (dashboardType() === 'cashbook') {
      <div class="flex w-full flex-col divide-y divide-border-divider-alternative">
        <div class="flex min-h-[5.5rem] w-full flex-row items-center px-4 py-2">
          <h1>Not implemented yet</h1>
        </div>
        <div class="p-4"></div>
      </div>
    }
    <!-- End Right part   -->
  </div>
</div>
