import { Async<PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { LinkboxLinkComponent } from '@/app/shared/atoms/linkbox-link/linkbox-link.component';
import { IconDocumentPdfComponent } from '@/app/shared/icons/document-pdf/document-pdf.component';
import { IconLinkComponent } from '@/app/shared/icons/link/link.component';
import { LinkboxComponent } from '@/app/shared/molecules/linkbox/linkbox.component';
import { ToSecureFileUrlPipe } from '@/app/shared/pipes/to-secure-file-url.pipe';

@Component({
  selector: 'fish-qualification-proof-linkbox',
  imports: [
    IconDocumentPdfComponent,
    IconLinkComponent,
    LinkboxComponent,
    LinkboxLinkComponent,
    NgIf,
    TranslateModule,
    ToSecureFileUrlPipe,
    AsyncPipe,
  ],
  templateUrl: './qualification-proof-linkbox.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class QualificationProofLinkboxComponent {
  @Input() public federalStateId!: string;

  constructor(private readonly translate: TranslateService) {}

  protected get templateFileUrl(): string {
    return this.translate.instant(`help.template.${this.federalStateId.toLowerCase()}`);
  }

  protected get antiforgeryFileUrl(): string {
    return this.translate.instant(`help.antiforgery_guide.${this.federalStateId.toLowerCase()}`);
  }
}
