import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { RouterModule } from '@angular/router';

@Component({
  templateUrl: './linkbox-link.component.html',
  selector: 'fish-linkbox-link',
  imports: [RouterModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LinkboxLinkComponent {
  @Input() public href: string = '#';

  @Input() public disabled: boolean = false;

  protected onClick(): void {
    window.open(this.href, '_blank');
  }
}
