import { ChangeDetectionStrategy, Component, output } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { ReportingsDashboardType } from '@/app/features/reportings/components/templates/reportings-dashboard-template/reportings-dashboard-template.models';
import { SliderButtonComponent } from '@/app/shared/atoms/slider-button/slider-button.component';
import { IconCashbookComponent } from '@/app/shared/icons/cashbook/cashbook.component';
import { IconStatisticComponent } from '@/app/shared/icons/statistic/statistic.component';
import { SliderButtonGroupComponent } from '@/app/shared/molecules/slider-button-group/slider-button-group.component';

@Component({
  selector: 'fish-reportings-slider-button',
  standalone: true,
  imports: [SliderButtonComponent, SliderButtonGroupComponent, TranslateModule, IconStatisticComponent, IconCashbookComponent],
  templateUrl: './reportings-slider-button.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReportingsSliderButtonComponent {
  // Outputs
  public readonly changed = output<ReportingsDashboardType>();

  protected handleSliderButtonChanged(newIndex: number): void {
    if (newIndex === 0) {
      this.changed.emit(ReportingsDashboardType.statistics);
    } else if (newIndex === 1) {
      this.changed.emit(ReportingsDashboardType.cashbook);
    } else {
      throw new Error(`Button Slider index out of bounds for index: ${newIndex}`);
    }
  }
}
