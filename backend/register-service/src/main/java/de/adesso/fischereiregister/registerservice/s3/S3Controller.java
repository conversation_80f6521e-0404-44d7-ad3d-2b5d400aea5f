package de.adesso.fischereiregister.registerservice.s3;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/storage")
public class S3Controller {

    private final S3Service s3Service;

    public S3Controller(S3Service s3Service) {
        this.s3Service = s3Service;
    }

    @GetMapping("/**")
    public ResponseEntity<byte[]> downloadFile(HttpServletRequest request) {
        String fileKey = request.getRequestURI().split(request.getContextPath() + "/storage/")[1];
        S3ObjectResponse response = s3Service.downloadFile(fileKey);
        HttpHeaders headers = new HttpHeaders();
        String fileName = fileKey.replace("/", "_");
        headers.setContentDispositionFormData("attachment", fileName);
        if (response.getContentType() != null) {
            headers.setContentType(MediaType.parseMediaType(response.getContentType()));
        }
        return ResponseEntity
                .ok()
                .headers(headers)
                .body(response.getContent());
    }
}


