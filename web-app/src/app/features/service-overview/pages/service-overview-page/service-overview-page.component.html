<fish-page-content data-testid="service-overview">
  @if (registerEntry()) {
    <div class="mt-4 flex h-page-content-sm w-full flex-col gap-4">
      @if (isPersonBanned()) {
        <fish-ban-notice (deleteButtonClicked)="onDeleteBanClick()" data-testid="service-overview-ban-notice" />
      }
      @if (!isJurisdictionMatching()) {
        <fish-jurisdiction-notice data-testid="service-overview-jurisdiction-notice" />
      }
      <div class="flex flex-grow flex-col items-stretch justify-between">
        <div class="flex max-h-[620px] min-h-0 flex-1 items-stretch gap-4">
          <fish-tax-service-card
            class="block basis-1/3"
            data-testid="service-overview-tax-card"
            [taxes]="registerEntry()!.taxes"
            [userFederalState]="userFederalState()!"
          />

          <div class="basis-1/3">
            <fish-license-service-card
              data-testid="service-overview-license-card"
              [certificate]="certificate()"
              [userFederalState]="userFederalState()!"
              [licenses]="registerEntry()?.fishingLicenses ?? []"
              [jurisdiction]="registerEntry()?.jurisdiction!"
              [isJurisdictionMatching]="isJurisdictionMatching()"
            />
          </div>

          <div class="basis-1/3">
            <fish-document-service-card data-testid="service-overview-document-card" [documents]="registerEntry()?.identificationDocuments ?? []" />
          </div>
        </div>

        <div class="inline-flex w-full flex-col items-center justify-start gap-2.5 pb-6">
          <fish-interaction-menu [isPersonBanned]="isPersonBanned()" [isJurisdictionMatching]="isJurisdictionMatching()" />
        </div>
      </div>
    </div>
    <fish-confirm-delete-ban-dialog (confirmDeleteButtonClicked)="onConfirmDeleteBanClick($event)" [isLoading]="isDeleteBanLoading()" />
  }
</fish-page-content>
