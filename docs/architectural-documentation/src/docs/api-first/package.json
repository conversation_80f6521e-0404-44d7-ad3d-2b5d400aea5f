{"name": "fishereiregister", "version": "1.14.1", "type": "module", "dependencies": {"@redocly/cli": "^1.25.4", "@typespec/compiler": "^0.57.0", "@typespec/http": "^0.57.0", "@typespec/openapi": "^0.57.0", "@typespec/openapi3": "^0.57.0", "@typespec/rest": "^0.57.0", "@typespec/versioning": "^0.57.0", "cross-env": "^7.0.3", "openapi-to-postmanv2": "^4.20.0", "rimraf": "^5.0.5"}, "devDependencies": {"@openapitools/openapi-generator-cli": "^2.14.0", "@typespec/prettier-plugin-typespec": "^0.57.0", "concurrently": "^8.2.2", "http-server": "^14.1.1", "nodemon": "^2.0.22", "prettier": "3.2.5"}, "private": true, "scripts": {"compile": "tsp compile src/main.tsp", "redoc": "cross-env-shell redocly build-docs tsp-output/@typespec/openapi3/openapi.Fischereiregister.${npm_package_version}.yaml", "build": "npm run compile && npm run docs", "build:java": "npm run compile && npm run generate:java", "build:angular": "npm run compile && npm run generate:angular", "dev": "npm run build && http-server ./build", "docs": "npm run redoc -- --output build/index.html", "generate:java": "cross-env-shell openapi-generator-cli generate -g spring -i tsp-output/@typespec/openapi3/openapi.Fischereiregister.${npm_package_version}.yaml -o build/openapi-spring -c spring.config.yaml -t templates/spring", "generate:angular": "cross-env-shell openapi-generator-cli generate -g typescript-angular -i tsp-output/@typespec/openapi3/openapi.Fischereiregister.${npm_package_version}.yaml -o build/openapi-angular"}}