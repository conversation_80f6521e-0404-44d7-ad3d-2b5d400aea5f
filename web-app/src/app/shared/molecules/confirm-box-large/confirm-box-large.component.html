<fish-focus-ring>
  <div [class]="classes()">
    <div [class]="headerClasses()">
      <fish-checkbox [externalFocusRing]="true" [formControl]="formControl()" class="gap-4">
        <div class="flex flex-col p-1">
          <span class="flex-1 font-bold group-has-[:checked]:text-action-primary">
            {{ label() }}
          </span>
          <span [innerHTML]="subLabel()" class="flex-1"></span>
        </div>
      </fish-checkbox>
    </div>
    <div class="flex rounded-b">
      <ng-content />
    </div>
  </div>
</fish-focus-ring>
