import { ChangeDetectionStrategy, Component, signal } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { ReportingsSliderButtonComponent } from '@/app/features/reportings/components/organisms/reportings-slider-button/reportings-slider-button.component';
import { StatisticsDashboardComponent } from '@/app/features/reportings/components/organisms/statistics-dashboard/statistics-dashboard.component';
import { ReportingsDashboardType } from '@/app/features/reportings/components/templates/reportings-dashboard-template/reportings-dashboard-template.models';
import { IconStatisticComponent } from '@/app/shared/icons/statistic/statistic.component';

@Component({
  selector: 'fish-reportings-dashboard',
  standalone: true,
  imports: [TranslateModule, IconStatisticComponent, ReportingsSliderButtonComponent, StatisticsDashboardComponent],
  templateUrl: './reportings-dashboard-template.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReportingsDashboardTemplateComponent {
  protected readonly dashboardType = signal<ReportingsDashboardType>(ReportingsDashboardType.statistics);

  protected handleReportingsSliderButtonChanged(dashboardType: ReportingsDashboardType): void {
    this.dashboardType.set(dashboardType);
  }
}
