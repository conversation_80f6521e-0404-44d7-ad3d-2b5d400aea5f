<div class="flex h-fit flex-col pb-6">
  <div class="flex h-14 w-full justify-stretch" data-testid="tab-group">
    @for (tab of tabs; let i = $index; track i) {
      <div class="flex-1 has-[:focus-visible]:z-10" [ngClass]="isFirstOfSection(tab) ? 'ml-6' : ''">
        <fish-tab-title
          [active]="i === activeIndex()"
          [disabled]="tab.disabled()"
          (clicked)="setActiveTab(i)"
          [dataTestId]="tab.labelButtonDataTestId()"
        >
          {{ tab.label() }}
        </fish-tab-title>
      </div>
    }
  </div>

  <div class="relative mt-6 flex-1">
    <ng-template cdkPortalOutlet></ng-template>
  </div>
</div>
