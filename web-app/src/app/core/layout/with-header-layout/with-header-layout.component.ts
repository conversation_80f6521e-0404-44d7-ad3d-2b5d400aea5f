import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';

import { BackgroundGradientComponent } from '@/app/core/layout/background-gradient/background-gradient.component';
import { BackgroundGraphicComponent } from '@/app/core/layout/background-graphic/background-graphic.component';
import { HeaderComponent } from '@/app/core/layout/header/header.component';
import { ProfileHeaderComponent } from '@/app/core/layout/profile-header/profile-header.component';

@Component({
  selector: 'fish-with-header-layout',
  imports: [BackgroundGradientComponent, BackgroundGraphicComponent, HeaderComponent, ProfileHeaderComponent, RouterOutlet],
  templateUrl: './with-header-layout.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class WithHeaderLayoutComponent {}
