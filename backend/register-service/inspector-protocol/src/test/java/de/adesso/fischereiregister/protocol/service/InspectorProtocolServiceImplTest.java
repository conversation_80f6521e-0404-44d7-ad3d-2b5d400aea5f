package de.adesso.fischereiregister.protocol.service;

import de.adesso.fischereiregister.protocol.persistence.InspectorProtocol;
import de.adesso.fischereiregister.protocol.persistence.InspectorProtocolRepository;
import de.adesso.fischereiregister.protocol.persistence.result.model.ActiveInspectorsResult;
import de.adesso.fischereiregister.protocol.persistence.result.model.NumberOfInspectionsResult;
import de.adesso.fischereiregister.protocol.service.model.InspectionsStatisticsResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class InspectorProtocolServiceImplTest {

    private InspectorProtocolRepository inspectorProtocolRepository;
    private InspectorProtocolServiceImpl inspectorProtocolService;

    @BeforeEach
    void setUp() {
        inspectorProtocolRepository = mock(InspectorProtocolRepository.class);
        inspectorProtocolService = new InspectorProtocolServiceImpl(inspectorProtocolRepository);
    }

    @Test
    @DisplayName("createProtocolEntry should save the protocol entry with correct values")
    void testCreateProtocolEntry() {
        // Arrange
        String inspectorUserId = "user123";
        String registerEntryId = "entry456";
        String inspectorFederalState = "StateX";
        LocalDateTime inspectionTimestamp = LocalDateTime.now();

        // Act
        inspectorProtocolService.createProtocolEntry(inspectorUserId, registerEntryId, inspectorFederalState, inspectionTimestamp);

        // Assert
        ArgumentCaptor<InspectorProtocol> captor = ArgumentCaptor.forClass(InspectorProtocol.class);
        verify(inspectorProtocolRepository, times(1)).save(captor.capture());

        InspectorProtocol savedProtocol = captor.getValue();
        assertEquals(inspectorUserId, savedProtocol.getInspectorUserId());
        assertEquals(registerEntryId, savedProtocol.getRegisterEntryId());
        assertEquals(inspectorFederalState, savedProtocol.getInspectorFederalState());
        assertEquals(inspectionTimestamp, savedProtocol.getInspectionTimestamp());
    }

    @Test
    void testCreateProtocolEntryWithNullRegisterEntryId() {
        // Arrange
        String inspectorUserId = "user123";
        String registerEntryId = null;
        String inspectorFederalState = "StateX";
        LocalDateTime inspectionTimestamp = LocalDateTime.now();

        // Act
        inspectorProtocolService.createProtocolEntry(inspectorUserId, registerEntryId, inspectorFederalState, inspectionTimestamp);

        // Assert
        ArgumentCaptor<InspectorProtocol> captor = ArgumentCaptor.forClass(InspectorProtocol.class);
        verify(inspectorProtocolRepository, times(1)).save(captor.capture());

        InspectorProtocol savedProtocol = captor.getValue();
        assertEquals(inspectorUserId, savedProtocol.getInspectorUserId());
        assertNull(savedProtocol.getRegisterEntryId());
        assertEquals(inspectorFederalState, savedProtocol.getInspectorFederalState());
        assertEquals(inspectionTimestamp, savedProtocol.getInspectionTimestamp());
    }

    @Test
    void testGetInspectionsStatisticsWithValidData() {
        // Arrange
        String federalState = "StateA";
        List<Integer> years = List.of(2021, 2023);

        List<ActiveInspectorsResult> activeInspectorsResults = List.of(
                new ActiveInspectorsResult(10, 2021),
                new ActiveInspectorsResult(15, 2023)
        );
        List<NumberOfInspectionsResult> numberOfInspectionsResults = List.of(
                new NumberOfInspectionsResult(5, 2021),
                new NumberOfInspectionsResult(8, 2023)
        );

        when(inspectorProtocolRepository.selectActiveInspectors(years,federalState)).thenReturn(activeInspectorsResults);
        when(inspectorProtocolRepository.selectNumberOfInspections(years,federalState)).thenReturn(numberOfInspectionsResults);

        // Act
        List<InspectionsStatisticsResult> result = inspectorProtocolService.getInspectionsStatistics(years, federalState);

        // Assert
        verify(inspectorProtocolRepository, times(1)).selectActiveInspectors(years,federalState);
        verify(inspectorProtocolRepository, times(1)).selectNumberOfInspections(years,federalState);
        assertEquals(2, result.size());
        assertEquals(2021, result.get(0).getYear());
        assertEquals(2023, result.get(1).getYear());
    }

    @Test
    void testGetInspectionsStatisticsWithEmptyYears() {
        // Arrange
        String federalState = "StateA";
        List<Integer> years = List.of();

        List<ActiveInspectorsResult> activeInspectorsResults = List.of(
                new ActiveInspectorsResult(10, 2021),
                new ActiveInspectorsResult(15, 2023)
        );
        List<NumberOfInspectionsResult> numberOfInspectionsResults = List.of(
                new NumberOfInspectionsResult(5, 2021),
                new NumberOfInspectionsResult(8, 2023)
        );

        when(inspectorProtocolRepository.selectActiveInspectors(null, federalState)).thenReturn(activeInspectorsResults);
        when(inspectorProtocolRepository.selectNumberOfInspections(null, federalState)).thenReturn(numberOfInspectionsResults);

        // Act
        List<InspectionsStatisticsResult> result = inspectorProtocolService.getInspectionsStatistics(years, federalState);

        // Assert
        verify(inspectorProtocolRepository, times(1)).selectActiveInspectors(null, federalState);
        verify(inspectorProtocolRepository, times(1)).selectNumberOfInspections(null, federalState);
        assertEquals(2, result.size());
    }

    @Test
    void testGetInspectionsStatisticsWithNullFederalState() {
        // Arrange
        String federalState = null;
        List<Integer> years = List.of(2021);

        List<ActiveInspectorsResult> activeInspectorsResults = List.of(
                new ActiveInspectorsResult(10, 2021),
                new ActiveInspectorsResult(15, 2023)
        );
        List<NumberOfInspectionsResult> numberOfInspectionsResults = List.of(
                new NumberOfInspectionsResult(5, 2021),
                new NumberOfInspectionsResult(8, 2023)
        );

        when(inspectorProtocolRepository.selectActiveInspectors(years, federalState)).thenReturn(activeInspectorsResults);
        when(inspectorProtocolRepository.selectNumberOfInspections(years, federalState)).thenReturn(numberOfInspectionsResults);

        // Act
        List<InspectionsStatisticsResult> result = inspectorProtocolService.getInspectionsStatistics(years, federalState);

        // Assert
        verify(inspectorProtocolRepository, times(1)).selectActiveInspectors(years, federalState);
        verify(inspectorProtocolRepository, times(1)).selectNumberOfInspections(years, federalState);
        assertEquals(2, result.size());
        assertEquals(2021, result.get(0).getYear());
    }
}