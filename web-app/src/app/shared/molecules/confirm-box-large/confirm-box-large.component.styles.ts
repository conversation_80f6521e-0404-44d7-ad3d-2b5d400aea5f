import { cva } from 'class-variance-authority';

export const confirmBoxLargeStyles = cva(
  ['group', 'flex', 'flex-col', 'items-stretch', 'rounded', 'backdrop-blur-xl', 'transition-all', 'duration-240', 'ease-out'],
  {
    variants: {
      variant: {
        primary: [
          'bg-tint-white',
          'shadow-glass-white-tint',
          'active:bg-tint-blue',
          'active:shadow-glass-blue-tint-pressed',
          'has-[:checked]:bg-tint-blue',
          'has-[:checked]:shadow-glass-blue-tint',
          'has-[:checked]:active:bg-tint-white',
          'has-[:checked]:active:shadow-glass-white-tint',
        ],
        error: ['bg-feedback-background-warning-2', 'shadow-glass-red-tint'],
      },
    },
    defaultVariants: {
      variant: 'primary',
    },
  }
);

export const confirmBoxLargeHeaderStyles = cva(
  ['flex', 'items-center', 'gap-4', 'rounded-t', 'pl-6', 'pr-8', 'transition-all', 'duration-240', 'ease-out'],
  {
    variants: {
      variant: {
        primary: [
          'group-hover:bg-action-secondary-hover',
          'group-active:bg-action-secondary-pressed',
          'group-has-[:checked]:bg-noticearea-fill-checked',
          'group-has-[:checked]:shadow-glass-blue-tint',
          'group-has-[:checked]:group-hover:bg-action-secondary-hover',
          'group-has-[:checked]:group-active:bg-action-secondary-pressed',
          'shadow-glass-white-tint',
          'bg-noticearea-fill',
        ],
        error: ['bg-feedback-background-warning-1', 'shadow-glass-red-tint'],
      },
    },
    defaultVariants: {
      variant: 'primary',
    },
  }
);
