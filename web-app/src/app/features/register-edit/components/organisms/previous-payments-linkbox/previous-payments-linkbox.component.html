<fish-linkbox
  direction="vertical"
  [header]="'edit_form.previous_payments.help.header' | translate"
  [alternativeText]="'edit_form.previous_payments.help.alternative_text' | translate"
>
  <ng-container *ngIf="federalStateId">
    <fish-linkbox-link [href]="('edit_form.previous_payments.help.template.url' | translate | toSecureFileUrl | async) ?? '#'">
      <fish-icon-link icon size="48" data-testid="previous-payment-help-template" />
      {{ 'edit_form.previous_payments.help.template.link_text' | translate }}
    </fish-linkbox-link>
    <fish-linkbox-link [href]="('edit_form.previous_payments.help.antiforgery_guide.url' | translate | toSecureFileUrl | async) ?? '#'">
      <fish-icon-document-pdf icon size="48" data-testid="previous-payment-help-antiforgery" />
      {{ 'edit_form.previous_payments.help.antiforgery_guide.link_text' | translate }}
    </fish-linkbox-link>
  </ng-container>
</fish-linkbox>
