import { ChangeDetectionStrategy, Component } from '@angular/core';

import { ReportingsDashboardTemplateComponent } from '@/app/features/reportings/components/templates/reportings-dashboard-template/reportings-dashboard-template.component';

@Component({
  selector: 'fish-reportings-page',
  standalone: true,
  imports: [ReportingsDashboardTemplateComponent],
  templateUrl: './reportings-page.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReportingsPageComponent {}
