import { CdkConnectedOverlay, CdkOverlayOrigin, ScrollStrategy, ScrollStrategyOptions } from '@angular/cdk/overlay';
import { ChangeDetectionStrategy, Component, ElementRef, HostListener, OnInit, computed, inject, input, signal, viewChild } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { twMerge } from 'tailwind-merge';

import { UserService } from '@/app/core/services/user/user.service';
import { dropdownOverlayAnimation } from '@/app/shared/animations/dropdown-overlay.animations';
import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { FocusRingComponent } from '@/app/shared/atoms/focus-ring/focus-ring.component';
import { headerToggleButtonComponentStyles } from '@/app/shared/atoms/header-toggle-button/header-toggle-button.component.styles';
import { ProfileIconComponent } from '@/app/shared/atoms/profile-icon/profile-icon.component';
import { IconSignoutComponent } from '@/app/shared/icons/signout/signout.component';

@Component({
  selector: 'fish-user-dropdown-menu',
  imports: [FocusRingComponent, CdkConnectedOverlay, CdkOverlayOrigin, TranslateModule, ProfileIconComponent, ButtonComponent, IconSignoutComponent],
  templateUrl: './user-dropdown-menu.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [dropdownOverlayAnimation],
})
export class UserDropdownMenuComponent implements OnInit {
  // Inputs
  public readonly isActive = input<boolean>(false);

  // Fields
  protected readonly menuIsOpen = signal(false);

  protected readonly classes = computed(() => twMerge(headerToggleButtonComponentStyles({ isActive: this.menuIsOpen() })));

  protected readonly overlay = viewChild(CdkConnectedOverlay);

  protected readonly toggleButtonComponent = viewChild('toggleButton', { read: ElementRef });

  protected readonly logoutButtonComponent = viewChild('logoutButton', { read: ButtonComponent });

  protected readonly toggleButtonElement = computed<HTMLButtonElement | null>(() => this.toggleButtonComponent()?.nativeElement ?? null);

  protected readonly logoutButtonElement = computed<HTMLButtonElement | null>(
    () => this.logoutButtonComponent()?.buttonElement()?.nativeElement ?? null
  );

  private readonly scrollStrategyOptions = inject(ScrollStrategyOptions);

  protected scrollStrategy!: ScrollStrategy;

  // Dependencies
  private readonly userService = inject(UserService);

  protected handleLogout(): void {
    this.userService.logout$().subscribe();
  }

  public ngOnInit(): void {
    this.scrollStrategy = this.scrollStrategyOptions.reposition();
  }

  protected toggleMenu(): void {
    if (this.menuIsOpen()) {
      this.closeMenu();
    } else {
      this.openMenu();
    }
  }

  protected openMenu(): void {
    this.menuIsOpen.set(true);
  }

  protected closeMenu(): void {
    this.menuIsOpen.set(false);
  }

  protected onToggleButtonFocus(event: FocusEvent): void {
    // Check if focus came from keyboard navigation (not mouse click)
    const target = event.target as HTMLElement;
    if (target && target.matches(':focus-visible') && !this.menuIsOpen()) {
      this.openMenu();
      // Don't automatically focus the logout button - wait for Tab key
    }
  }

  protected onToggleButtonBlur(_event: FocusEvent): void {
    // Close menu when focus leaves the toggle button, unless focus is moving to the logout button
    setTimeout(() => {
      const activeElement = document.activeElement;
      const logoutButtonElement = this.logoutButtonElement();

      // Only close if focus didn't move to the logout button
      if (this.menuIsOpen() && activeElement !== logoutButtonElement) {
        this.closeMenu();
      }
    }, 10);
  }

  @HostListener('document:keydown', ['$event'])
  protected onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Tab' && this.menuIsOpen()) {
      const activeElement = document.activeElement;

      if (activeElement === this.toggleButtonElement() && !event.shiftKey) {
        // Tab from toggle button to logout button
        event.preventDefault();
        event.stopPropagation();
        this.logoutButtonElement()?.focus();
      } else if (activeElement === this.logoutButtonElement() && !event.shiftKey) {
        // Tab from logout button => close menu and focus toggle button to return back to normal tab flow
        // This is a bit hacky but the simplest solution for now as long as it's acceptable from an accessibility perspective
        this.closeMenu();
        this.toggleButtonElement()?.focus();
      } else if (activeElement === this.logoutButtonElement() && event.shiftKey) {
        // Shift+Tab from logout button => back to toggle button
        event.preventDefault();
        event.stopPropagation();
        this.toggleButtonElement()?.focus();
      }
    }
  }

  @HostListener('window:resize')
  protected onWindowResize(): void {
    // Force the overlay to recalculate its position when window resizes
    if (this.menuIsOpen() && this.overlay) {
      this.overlay()?.overlayRef?.updatePosition();
    }
  }
}
