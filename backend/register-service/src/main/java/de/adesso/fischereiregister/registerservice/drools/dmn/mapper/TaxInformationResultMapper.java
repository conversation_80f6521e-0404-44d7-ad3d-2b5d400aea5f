package de.adesso.fischereiregister.registerservice.drools.dmn.mapper;

import de.adesso.fischereiregister.core.ports.contracts.TaxPriceInformation;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationOutput;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "default")
public interface TaxInformationResultMapper {
    TaxInformationResultMapper INSTANCE = Mappers.getMapper(TaxInformationResultMapper.class);

    @Mapping(target = "price", source = "summOfTaxAmount")
    TaxPriceInformation toCoreModel(TaxInformationOutput result);
}
