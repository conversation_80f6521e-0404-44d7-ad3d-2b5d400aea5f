{"application": {"tenant": "SH", "title": "Fischerei Fachverfahren"}, "common": {"federal_state": "Schleswig-Holstein", "cancel": "Abbrechen", "optional": "Optional", "start_page": "Startseite", "form": {"error": {"required": "Das Feld \"{{control}}\" ist ein Pflichtfeld. Prüfen Sie Ihre Eingabe.", "pattern": "Das Feld \"{{control}}\" enthält ungültige Zeichen. Prüfen Sie Ihre Eingabe.", "only_numbers": "<PERSON><PERSON> darf nur Zahlen enthalten.", "min_length": "Der eingegebene Wert für \"{{control}}\" ist zu kurz (minimal {{length}} Zeichen). G<PERSON>en sie längere Bezeichnung ein.", "max_length": "Der eingegebene Wert für \"{{control}}\" ist zu lang (maximal {{length}} Zeichen). G<PERSON>en sie kürzere Bezeichnung ein.", "date": {"format": "Das Format entspricht nicht TT.MM.JJJJ. Prüfen Sie ihre Eingabe.", "not_in_future": "Das Datum darf nicht in der Zukunft liegen. Prüfen Sie Ihre Eingabe.", "not_in_past": "Das Datum darf nicht in der Vergangenheit liegen. Prüfen Sie Ihre Eingabe.", "min": "Das Datum muss nach {{date}} liegen", "max": "Der eingegebene Wert für \"{{control}}\" ist zu lang. Geben Sie eine kürzere Bezeichnung ein."}, "birthdate": {"not_in_future": "Das Geburtsdatum muss in der Vergangenheit liegen. Korrigieren Sie Ihre Eingabe.", "recent": "Das Geburtsdatum liegt zu weit in der Vergangenheit. Prüfen Sie Ihre Eingabe."}, "email": "Sie müssen eine gültige E-Mail Adresse eingeben", "email_confirmation": "Die E-Mail Adressen stimmen nicht überein", "combobox": "<PERSON>te wählen Si<PERSON> einen Wert aus der Liste \"{{control}}\" aus.", "postcode": {"min_length": "Der eingegebene Wert für \"PLZ\" ist zu kurz. Geben Sie eine 5-stellige Nummer ein.", "max_length": "Der eingegebene Wert für \"PLZ\" ist zu lang. Geben Sie eine 5-stellige Nummer ein.", "pattern": "Der eingegebene Wert für „PLZ“ ist falsch. Geben Sie nur Ziffern an."}}}, "loading": "Wird bearbeitet...", "button": {"back": "Zurück", "save_and_continue": "Speichern & Weiter", "confirm_and_continue": "Bestätigen & Weiter", "finnish": "Speichern & Ausstellen", "edit": "<PERSON><PERSON><PERSON>", "confirm": "Bestätigen", "login": "<PERSON><PERSON>", "print": "<PERSON><PERSON><PERSON>", "send": "Senden"}, "time": {"period_format": "{{from}} - {{to}}", "indefinite": "Unbegrenzt"}, "server_error_dialog": {"header": "<PERSON>nst nicht erreichbar", "content": {"top": "Ihre Daten können aktuell nicht gespeichert werden.", "middle": {"text": "Häufige Ursachen sind Wartungsarbeiten oder zu viele Anfragen an den Server. Lassen Sie den Tab geöffnet und senden Sie die Informationen ", "hint": "in 15 Minuten noch einmal."}, "bottom": {"text": "Bleibt das Problem bestehen, kontaktieren Sie bitte Ihre fachliche Leitstelle:", "email": "<PERSON><PERSON>@fachlicheleitstelle-nrw.de"}}, "footer": {"button": "OK"}}, "amount": {"euro": "€", "unit": "Stk."}}, "header": {"federal_state_logo": {"url": "https://upload.wikimedia.org/wikipedia/commons/thumb/e/e4/Schleswig-Holstein.svg/800px-Schleswig-Holstein.svg.png"}, "user": {"government_office": "%keycloak:officeAddress.office", "examination_issuer": "%keycloak:examination.issuer", "display_name": "%keycloak:name"}}, "tax_cost_options": {"option_1": {"option_type": "stepper", "duration_label": "<PERSON><PERSON>", "cost_per_year": "23", "min_years": "1", "max_years": "4"}, "option_2": {"option_type": "static", "duration_label": "Maximum", "cost": "92", "years": "4"}}, "fee_cost_options": {"standard": {"duration": "indefinite", "cost": "31"}, "vacation": {"duration": "2W", "cost": "20"}, "special": {"duration": "1Y", "cost": "20"}}, "edit_form": {"previous_payments": {"help": {"antiforgery_guide": {"link_text": "Bekannte Fälschungsmerkmale", "url": "https://www.ietf.org/rfc/rfc2324.txt.pdf"}, "template": {"link_text": "Muster", "url": "https://ec.europa.eu/commission/presscorner/api/files/document/print/en/ip_20_940/IP_20_940_EN.pdf"}, "header": "Überprüfungshilfe", "alternative_text": "Etwas ist schiefgelaufen. Ihr Konto konnte keinem Bundesland zugeordnet werden."}, "text": "Überprüfen & Daten eintragen", "subtext": "Es können lediglich Fischereiabgaben mit <b>aktueller</b> und <b>zukünftiger Gültigkeit</b>  für {{federalState}} eingetragen werden.", "payments_available_checkbox": "Abgaben bereits bezahlt", "validTo": "Bis"}, "tab_titles": {"personal_data": "Personendaten", "qualification_proof": "<PERSON><PERSON><PERSON><PERSON>", "qualification_proofs": "Nachweise", "previous_payments": "Bezahlte Abgaben", "payments": "Gebühren & Abgaben", "documents": "Dokumente", "ban_reason": "Begründung", "ban_period": "Zeitraum", "ban_summary": "<PERSON><PERSON><PERSON>"}, "data": {"title": "Titel", "title_placeholder": "—", "firstname": "<PERSON><PERSON><PERSON>", "firstname_placeholder": "<PERSON><PERSON><PERSON>", "lastname": "Name", "lastname_placeholder": "Name", "birthname": "Geburtsname", "birthname_sub_label": "<PERSON><PERSON>n sich der Name geändert hat", "birthname_placeholder": "Geburtsname", "birthdate": "Geburtsdatum", "birthdate_placeholder": "TT.MM.JJJJ", "birthplace": "Geburtsort", "birthplace_placeholder": "Geburtsort", "nationality": "Staatsangehörigkeit", "nationality_placeholder": "Staatsangehörigkeit"}, "address": {"title": "Anschrift", "no_permanent_residence_title": "<PERSON><PERSON> fester <PERSON><PERSON><PERSON>", "no_permanent_residence_description": "<PERSON><PERSON>ck-<PERSON>rte wird nach Abschluss des Vorgangs der <b>ausstellenden Behörde</b> zugesendet.", "street": "Straße", "street_placeholder": "Straße eingeben", "street_number": "Hausnummer", "street_number_placeholder": "12ABC", "postcode": "PLZ", "postcode_placeholder": "12345", "city": "Ort", "city_placeholder": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>ben", "detail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detail_placeholder": "wohnt bei / ...", "office": "Behördenname", "office_placeholder": "Behördenname e<PERSON>ben", "deliver_to": "<PERSON>", "deliver_to_placeholder": "zu Händen eingeben", "office_city_placeholder": "<PERSON><PERSON> e<PERSON>ben"}, "qualification_proof": {"valid_from": "Gültig vom", "valid_from_placeholder": "TT.MM.jjjj", "issued_on": "Ausgestellt am", "legacy_number": "<PERSON><PERSON><PERSON>", "legacy_number_placeholder": "XXXX XXXX XXXX", "number": "<PERSON><PERSON><PERSON>", "number_placeholder": "XXXX XXXX XXXX", "issued_by": "Ausstellende Behörde", "issued_by_placeholder": "Name der Einrichtung", "issued_by_institution": "Ausstellende Institution", "federal_state": "Bundesland", "federal_state_placeholder": "Bundesland der ausstellenden Behörde", "place": "Ursprungsland", "place_placeholder": "Land der ausstellenden Behörde (Staat / Bundesland / ...)", "help": {"header": "Überprüfungshilfe", "alternative_text": "Bitte wählen Si<PERSON> ein Bundesland aus.", "other_proof_description_text": "Sonstige Nachweise können sein:", "other_proof_description_bullet_points": {"_1": "Berufsausbildung", "_2": "Studiengang", "_3": "Vergleichbare Ausbildung im Ausland"}, "template_link_text": "Muster", "antiforgery_link_text": "Bekannte Fälschungsmerkmale"}, "text": "Überprüfen & Daten eintragen", "subtext": "Als Qualifikationsnachweis dient entweder das Zeugnis einer erfolgreich bestandenen Fischereischeinprüfung, der alte Fischereischein <b>oder</b> ein sonstiger Nachweis.", "button_license_text": "<PERSON><PERSON><PERSON><PERSON>", "button_certificate_text": "<PERSON><PERSON><PERSON><PERSON>", "button_other_proof_text": "Sons<PERSON>ger Nachweis"}, "payments": {"box_title": "Betrag", "tax": {"title": "Fischereiabgabe entrichten", "title_selected": "Fischereiabgabe", "indefinitely": "Unbegrenzt", "none": "<PERSON><PERSON>", "title_payed": "Bezahlte Fischereiabgaben"}, "fee": {"title": "<PERSON><PERSON><PERSON><PERSON>", "limit": "Befristung"}, "tax_dialog": {"title": "Fischereiabgabe", "from_year_label": "<PERSON><PERSON><PERSON> ab", "duration": "<PERSON><PERSON>", "maximum": "Maximum", "already_payed": "Abgabe bereits anderweitig bezahlt"}, "payment_method": {"title": "Gewählte Bezahlform", "subtitle": "Bitte auswählen", "card": "Kartenzahlung", "card_description": "Kassenautomat / Kartenlesegerät", "cash": "Barzahlung", "cash_description": "<PERSON><PERSON><PERSON> beim <PERSON>"}}, "documents": {"box": {"title": "Digitale Dokumente", "license": {"sub_text": "<PERSON>er", "main_text": "Fischerei<b>schein</b>"}, "tax": {"sub_text": "Digitale", "main_text": "Fischerei<b>abgabe</b>"}, "email": {"sub_text": "E-Mail-Versand", "main_text": "Dokumente per E-Mail verschicken"}}, "license_cardbox": {"image": "/assets/license-card-preview/license-card-regular.png", "title": "<PERSON><PERSON><PERSON><PERSON>", "text": "<p> Wird innerhalb der nächsten <b>7 bis 8 Wochen</b> automatisch zugestellt. </p> <p> <PERSON><PERSON> <b>keine <PERSON></b> an<PERSON><PERSON><PERSON>, wird sie der <b>ausstellenden Behörde</b> zugesendet. </p>"}}, "qualification_proof_confirm": {"name": "Name", "test_code": "Zugangscode", "passed_on": "<PERSON><PERSON><PERSON>", "test_institution": "Prüfungseinrichtung"}, "ban_reason": {"title": "Begründung", "reported_by": "<PERSON><PERSON><PERSON><PERSON> von", "reported_by_placeholder": "Behördenname", "file_number": "Aktenzeichen", "file_number_placeholder": "Aktenzeichen"}, "ban_period": {"title": "Zeitraum", "temporary": "<PERSON><PERSON><PERSON><PERSON>", "permanent": "Lebenslang", "from": "<PERSON><PERSON><PERSON> ab", "to": "<PERSON><PERSON>", "button": {"execute": "Sperrung durchführen"}, "error": {"ban_end_before_ban_start": "Das Datum \"<PERSON><PERSON> e<PERSON>\" darf nicht vor dem Datum \"Beginn ab\" liegen. Korrigieren Sie die Eingabe."}}, "ban_summary": {"text": "Sperrung erfolgreich!", "subtext": "Eintrag wurde erfolgreich im Fischereiregister aufgenommen.", "table_header": {"full_name": "Name", "from": "<PERSON><PERSON><PERSON><PERSON> ab", "to": "Gesperrt bis", "file_number": "<PERSON><PERSON> (AZ)"}}}, "send_documents_dialog": {"title": "Dokumente verschicken", "email": "E-Mail", "email_placeholder": "E-Mail Adresse eingeben", "email_confirmation": "E-Mail Bestätigen", "email_confirmation_placeholder": "E-Mail Adress<PERSON> wied<PERSON>n", "button": "Senden"}, "send_documents_success_feedback": {"text": "Dokumente wurden versendet"}, "consent": {"linkbox": {"infopage": {"url": "https://www.schleswig-holstein.de/DE/fachinhalte/F/fischerei/Startseite_Box_Angelfischerei", "link_text": "Infopage"}, "header": "Datenschutzerklärung", "print_pdf": {"link_text": "PDF drucken", "url": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"}}, "title": "DSGVO", "header": "Bevor es los<PERSON>hen kann ...", "gdpr": {"label": "Informationen zum Umgang mit Ihren personenbezogenen Daten", "sub_label": "Ich habe die Informationen zum Umgang mit meinen personenbezogenen Daten zur Kenntnis genommen."}, "self_disclosure": {"label": "Selbstauskunft", "sublabel": "In den <b>letzten fünf J<PERSON>ren</b> vor Abgabe dieser Erklärung nicht <b>rechtskräftig verurteilt</b> oder mit einem  <b><PERSON><PERSON><PERSON><PERSON><PERSON></b> belegt worden, <b>wegen</b>:", "offenses": {"_1": "Fischwilderei", "_2": "<PERSON><PERSON><PERSON><PERSON> von <PERSON>schen, Fischereigeräten", "_3": "Vorsätzliche Beschädigung von <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, die der Fischerei oder der Fischzucht dienen, oder <PERSON>", "_4": "Fälschung eines Fischereischeines oder einer sonstigen zur Ausübung der Fischerei erforderlichen Bescheinigung", "_5": "Verstoßes gegen fischerei-, tierseuchen- oder wasserrechtliche Vorschriften oder wegen Tierquälerei oder Verstoßes gegen Naturschutzgesetze"}}}, "move_jurisdiction_consent": {"move_certificate_verified": {"subtext": "<PERSON><PERSON><PERSON><PERSON>, dass {{citizenFullname}} nach Schleswig-Holstein umgezogen ist, wurde vorgelegt."}, "title": "DSGVO - Zuständigkeit umziehen", "header": {"text": "ACHTUNG!", "subtext": "Sie sind im Begriff, <b>die Zuständigkeit</b> für <b>{{citizenFullname}}</b> in Ihr Bundesland <b>zu überführen.</b> "}, "warning_notice": {"text": "Diese Aktion kann nicht rückgängig gemacht werden.", "subtext": "<PERSON>den Si<PERSON> sich im Falle eines Fehlers bitte an eine:n Administrator:in."}, "info_notice": {"text": "Zukünftige <b>Zuständigkeit</b> für <b>{{citizenFullname}}</b> liegt in:"}, "form": {"text": "Um fortzufahren muss ein Nachweis vorliegen", "move_certificate_verified": {"text": "Nachweis wurde vorgelegt", "subtext": "<PERSON><PERSON><PERSON><PERSON>, dass {{citizenFullname}} nach dem aktuellen Bundesland umgezogen ist, wurde vorgelegt."}}}, "error": {"unauthorized": {"title": "401 : Zugang abgelaufen", "description": "Aus Sicherheitsgründen wurden Sie automatisch ausgeloggt."}, "not_found": {"title": "404: <PERSON><PERSON> fehlt", "description": "Die gesuchte Seite fehlt oder der Link wurde falsch eingegeben.", "button": "Zurück zur Startseite"}}, "alert": {"confirm": "Möchten Sie die Seite verlassen? Änderungen könnten verloren gehen."}, "notice": {"type": {"info": "Info", "warning": "<PERSON><PERSON><PERSON>", "banned": "<PERSON><PERSON><PERSON><PERSON>"}}, "profile": {"info": {"birthdate": "Geburtsdatum", "birthname": "Geburtsname", "birthplace": "Geburtsort", "federal_state": "Bundesland", "name": "Name", "identification": "<PERSON><PERSON><PERSON>"}}, "search": {"title": "Suchergebnisse", "bar": {"placeholder": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (TT.MM.JJJJ), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON>", "clear": "<PERSON><PERSON>"}, "results": {"count": "Die Suche ergab {{count}} Treffer.", "too-many-results": {"title": "Bitte präzisieren Sie ihre Suche.", "description": "<PERSON><PERSON> ergab zu viele Ergebnisse. Bitte ergänzen Sie weitere personenbezogene Daten.", "example_label": "Beispiel", "example": "<PERSON>, 24.03.2024, Hamburg"}, "no-results": {"title": "<PERSON><PERSON> gefunden", "description": "Bitte überprüfen Sie Ihre Suche. Ggf. existiert die Person noch nicht im Register."}, "button": {"inspect": "Einsehen"}}, "creation_menu": {"license_button": "<PERSON><PERSON><PERSON><PERSON>", "vacation_icon": "vacation", "vacation_button": "Urlauberfischereischein", "tax_button": "Fischereiabgabe", "special_button": "Sonderfischereischein"}}, "home_page": {"title": "Ausstellen & Verwalten", "information": "Bitte überprüfen Sie zunächst das Register nach möglichen Einträgen. <br/> In der Suche können Sie dann neue Einträge hinzufügen / Fischereischein digitalisieren / etc."}, "person_tax_create": {"title": "Fischereiabgabe"}, "digitize": {"title": "Fischereischein digitalisieren"}, "order_card": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> bestellen"}, "add_license": {"title": "Fischereischein beantragen"}, "pay_tax": {"title": "Fischereiabgabe bezahlen"}, "pay_tax_form": {"tab_titles": {"payments": "Abgaben", "documents": "Dokumente"}}, "ban_person": {"title": "Person <PERSON><PERSON>ren"}, "person_create_form": {"tab_titles": {"personal_data": "Personendaten", "payments": "Abgaben", "documents": "Dokumente"}}, "third_party_consent": {"label": "<PERSON><PERSON><PERSON> wur<PERSON> von Dr<PERSON> durchgeführt", "sub_label": "Ausweisdokumente und Vollmacht wurden eingesehen"}, "move_jurisdiction": {"title": "Zuständigkeit umziehen"}, "move_jurisdiction_form": {"tab_titles": {"payments": "Abgaben", "finish": "<PERSON><PERSON><PERSON>"}, "finish": {"header": {"text": "Zuständigkeits-Umzug erfolgreich!", "subtext": "Anpassung wurde wurde erfolgreich im Fischereiregister aufgenommen."}, "table": {"header_label_name": "Name", "header_label_jurisdiction": "Zuständiges Bundesland"}}}, "service_overview": {"title": "Serviceübersicht", "button": {"edit_person": "Personendaten bearbeiten", "ban_person": "Person sperren", "delete": "Registereintrag löschen", "pay_tax": "Fischereiabgabe bezahlen", "add_license": "Fischereischein beantragen", "menu_placeholder": "Registereintrag bearbeiten", "order_card": "Scheckkarte neubestellen"}, "service_card": {"manage_action": "<PERSON><PERSON><PERSON><PERSON>", "license_menu": {"title": "Hinzufügen", "license": "<PERSON><PERSON><PERSON><PERSON>", "vacation": "Urlauberfischereischein", "special": "Sonderfischereischein"}}, "tax_card": {"title": "Fischereiabgabe", "indefinite": "Lebenslang", "none": "<PERSON><PERSON>", "foreign_states": "In <b>anderen Bundesländern</b>", "no-taxes": "<PERSON><PERSON>gaben vorhanden"}, "license_card": {"title": "<PERSON><PERSON><PERSON><PERSON>", "valid_to": "Gültig bis", "valid_from": "Ausgestellt am", "title_labels": {"vacation": "Urlauberfischereischein", "special": "Sonderfischereischein", "regular": "<PERSON><PERSON><PERSON><PERSON>", "certificate": "Zugangscode vorhanden"}, "reached_limit": "<PERSON>it erreicht", "expired": "Abgelaufen", "no_licenses": "<PERSON><PERSON> v<PERSON>handen", "button": {"reorder": "Neu ausstellen", "extend": "Urlauberfischereischein verlängern", "digitize": "Fischereischein digitalisieren", "create_license": "<PERSON><PERSON><PERSON><PERSON>", "create_vacation_license": "Urlauberfischereischein", "create_limited_license": "Sonderfischereischein"}}, "document_card": {"title": "Dokumente", "tax": "Digitale Fischereiabgabe", "license": "<PERSON><PERSON>", "button_open": "<PERSON><PERSON><PERSON>"}}, "jurisdiction_notice": {"text": "Aktuelle <b>Zuständigkeit</b> für <b>{{citizenFullname}}</b> liegt in:", "text_no_jurisdiction": "B<PERSON><PERSON> keine <b>Zuständigkeit</b> für <b>{{citizenFullname}}</b> vergeben", "button": {"move_jurisdiction": "Zuständigkeit umziehen", "set_jurisdiction": "Zuständigkeit vergeben"}}, "ban_notice": {"permanent": "Lebenslang", "button": {"edit": "<PERSON><PERSON><PERSON>", "delete": "Entsperren"}}, "delete_ban_dialog": {"header": {"title": "Entsperren"}, "content": {"text1": "Sind Sie sicher?", "text2": "<b>{{citizenFullname}}</b> kann im Anschluss seinen Fischereischein wieder frei nutzen.", "text3": "<PERSON>te beach<PERSON> Si<PERSON>, dass eine Entsperrung nur durch einen Administrator zurückgenommen werden kann."}, "footer": {"button": "Entsperren"}}, "year_skipper": {"duration": {"years": {"singular": "Für 1 Jahr", "plural": "Für {{years}} J<PERSON>re"}}, "increment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decrement": "Verringern"}, "error_code_badge": {"label": "Fehlercode"}, "help": {"antiforgery_guide": {"bw": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf", "by": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf", "be": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf", "bb": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf", "hb": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf", "hh": "https://www.ietf.org/rfc/rfc2324.txt.pdf", "he": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf", "mv": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf", "ni": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf", "nw": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf", "rp": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf", "sl": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf", "sn": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf", "st": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf", "sh": "https://www.ietf.org/rfc/rfc2324.txt.pdf", "th": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"}, "template": {"bw": "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf", "by": "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf", "be": "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf", "bb": "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf", "hb": "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf", "hh": "https://ec.europa.eu/commission/presscorner/api/files/document/print/en/ip_20_940/IP_20_940_EN.pdf", "he": "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf", "mv": "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf", "ni": "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf", "nw": "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf", "rp": "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf", "sl": "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf", "sn": "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf", "st": "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf", "sh": "https://ec.europa.eu/commission/presscorner/api/files/document/print/en/ip_20_940/IP_20_940_EN.pdf", "th": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"}}, "value": {"titles": [{"id": "DR", "name": "DR."}, {"id": "HC", "name": "DR. HC."}, {"id": "EH", "name": "DR. EH."}], "federal_states": [{"id": "BW", "name": "Baden-Württemberg"}, {"id": "BY", "name": "Bayern"}, {"id": "BE", "name": "Berlin"}, {"id": "BB", "name": "Brandenburg"}, {"id": "HB", "name": "Bremen"}, {"id": "HH", "name": "Hamburg"}, {"id": "HE", "name": "Hessen"}, {"id": "MV", "name": "Mecklenburg-Vorpommern"}, {"id": "NI", "name": "Niedersachsen"}, {"id": "NW", "name": "Nordrhein-Westfalen"}, {"id": "RP", "name": "Rheinland-Pfalz"}, {"id": "SL", "name": "Saarland"}, {"id": "SN", "name": "Sachsen"}, {"id": "ST", "name": "Sachsen-Anhalt"}, {"id": "SH", "name": "Schleswig-Holstein"}, {"id": "TH", "name": "T<PERSON><PERSON><PERSON>en"}], "other_origin_place": [{"id": "OTHER", "name": "Sonstiges ausstellendes Land"}]}, "passed_exam_create": {"title": "Bestandene Prüfung anlegen", "summary": {"header": "Folgender Eintrag wird übermittelt:", "title": "Titel", "firstname": "<PERSON><PERSON><PERSON>", "lastname": "Name", "birthname": "Geburtsname", "birthdate": "Geburtsdatum", "birthplace": "Geburtsort", "passedOn": "Bestanden am", "examination_facility": "Prüfungseinrichtung"}, "aside": {"header": "Bestandene Prüfung anlegen", "text": {"top": "Bitte achten Sie auf eine korrekte Eingabe der Daten.", "bottom": "Anpassungen sind danach nur noch in der entsprechend zuständigen Behörde möglich."}}, "form": {"title": {"label": "Titel", "placeholder": "━"}, "firstname": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON>"}, "lastname": {"label": "Name", "placeholder": "Name"}, "birthname": {"label": "Geburtsname", "placeholder": "Geburtsname", "sublabel": "<PERSON><PERSON><PERSON> der Name sich geändert hat"}, "birthdate": {"label": "Geburtsdatum", "placeholder": "TT.MM.JJJJ"}, "birthplace": {"label": "Geburtsort", "placeholder": "Geburtsort"}, "passedOn": {"label": "Bestanden am"}}, "feedback": {"title": "Erfolgreich angelegt!", "subtitle": "Bestandene Prüfung wurde erfolgreich im Fischereiregister aufgenommen.", "table_header": {"full_name": "Name", "exam_code": "Zugangscode", "passed_on": "<PERSON><PERSON><PERSON>", "examination_facility": "Prüfungseinrichtung"}, "actions": {"print_confirmation": "Bestätigung drucken", "restart": "Neuen Eintrag hinzufügen"}}, "footer": {"button": {"confirm_and_check": "Bestätigen & Prüfen", "edit_data": "Daten an<PERSON>en", "send": "Senden"}}}, "statistics": {"tile": {"licenses": {"title": {"regular": "Fischerei<PERSON><PERSON>", "vacation": "Urlauber- / Ausländerfischereischeine", "limited": "Sonderfischereischeine"}, "title_subtext": "<PERSON><PERSON> Daten"}, "taxes_and_fees": {"title": "Fischereiabgaben", "title_subtext": "<PERSON><PERSON> Daten", "note": "Abgaben & Gebühren<br>inkl. U/A-Fischereischeine", "duration_one_year": "Gültig für 1 Jahr", "duration_multiple_years": "Gültig für mehrere Jahre"}, "certifications": {"title": "Prüfungen", "title_subtext": "<PERSON><PERSON> Daten"}}, "submission_type_table": {"attribute": "Attribut", "general": "Bundesland insg.", "online": "Onlinedienst", "analog": "Beh<PERSON><PERSON>"}, "issuers_table": {"attribute": "Attribut"}, "historical_bar_chart": {"completed": "Abgeschlossen", "not_completed": "Nicht Abgeschlossen"}}}