import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { ToSecureFileUrlPipe } from '@/app/shared/pipes/to-secure-file-url.pipe';

@Component({
  selector: 'fish-federal-state-logo',
  templateUrl: './federal-state-logo.component.html',
  imports: [TranslateModule, AsyncPipe, ToSecureFileUrlPipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FederalStateLogoComponent {}
